README文件包含开发时需要注意的各种事项，请即时关注README.md的每一次更新。

**通用规范**
* 建议少量多次commit；commit前请检查，避免修改他人编写的代码；push前请先pull；
* 新创建的模块需要引入sust-common；对于多个模块需要同时使用的实体Bean，请移至sust-common模块下，以避免代码冗余和循环依赖的问题
* 日期格式化请使用"yyyy-MM-dd HH:mm:ss"（HH为24小时计时法）；若使用"yyyy-MM-dd hh:mm:ss"应明确其为12小时计时；不要使用"YYYY-MM-dd HH:mm:ss"等
* 变量命名力求语义表达完整，不要中英混合，不要嫌名字长（编译型语言的变量名长度并不实际影响运行性能）
* POJO实体类的任何布尔类型变量，均不要以is开头，这可能会导致序列化错误
* Service/DAO层方法命名规范：
    * 获取单个对象的方法用get做前缀
    * 获取多个对象的方法用list做前缀，复数结尾
    * 获取统计值的方法用count做前缀
    * 插入的方法用save/insert做前缀
    * 删除的方法用remove/delete做前缀
    * 修改的方法用update做前缀
* 如果变量值仅在一个范围内变化，请用enum类型来定义，而不要直接使用字面量，难以管理、维护。
* Object的equals方法容易抛空指针异常，应使用常量或确定有值的对象来调用equals，如"test".equals(obj)
* 所有String类对象、整型包装类对象之间的值比较全部使用equals方法比较，而不要使用==
* 对于浮点数之间的等值比较，基本数据类型（如float）不要用==比较，包装数据类型（如Float）不能用equals比较，通常比较结果会与预期不符
* BigDecimal的等值比较用compareTo()，不要用equals
* 在表查询中，一律不要使用 * 作为查询的字段列表，需要哪些字段必须明确写明。说明如下：
    * 增加查询分析器解析成本
    * 增减字段容易与resultMap配置不一致
    * 无用字段增加网络消耗，尤其是text类型的字段
* 更新数据表记录时，必须同时更新记录对应的update_time字段为当前时间
* uniapp开发与ruoyi的element-ui在一些组件上存在差异，请在使用并遇到bug时详细阅读uniapp文档
* uniapp在开发不同平台（如H5、微信小程序等）时，组件的使用存在差异，请在使用并遇到bug时详细阅读uniapp文档
* 请养成写注释的习惯，尤其是对于稍复杂的逻辑，这对回忆代码、他人阅读代码、发现逻辑性bug、后期维护项目等均有好处，是应对命名不规范而带来阅读障碍的最简单直接的方法

**项目规范**
* controller层方法的返回值（即响应的对象）请使用R（top.sust.common.core.domain.R），而不是AjaxResult
* 为方便区分，小程序使用的接口尽量单独放在一个Contoller中，并以"Wx"开头进行命名，同时小程序使用的接口的访问路径请统一以"/wx"开头；小程序接口上不能使用@PreAuthorize("@ss.hasPermi('xxx')")等注解做访问控制
* 小程序接口的处理逻辑中，若需要使用到登录用户id或username，请调用WxSecurityUtils中的方法（top.sust.common.utils.WxSecurityUtils）。
* Mybatis-plus不要和PageHelper同时使用，可能发生bug，MP有自己的分页插件，详情查看MybatisPlusConfig
* 微信接口允许的查询条件应受到限制

**注意事项**
* 如果生成代码的编辑阶段，实体类的名字和数据库表名不一致，请使用@TableName注解指明实体类对应的数据库表名*

**待办**
* 在sys_menu中为自定义按钮设置权限；检查管理端所有controller接口是否都有@PreAuthorize，权限设置是否合理
