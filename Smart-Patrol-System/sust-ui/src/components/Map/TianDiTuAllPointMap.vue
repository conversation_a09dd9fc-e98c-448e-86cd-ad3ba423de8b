<template>
  <div class="tian-di-tu-map" ref="mapContainer"></div>
</template>

<script>
import TianDiTuMap from './TianDiTuMap.vue';

export default {
  // 继承基础地图组件
  extends: TianDiTuMap,
  name: 'TianDiTuAllPointMap',
  props: {
    // 新增点位数据属性
    points: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  // beforeUnmount钩子，确保组件销毁时清理地图实例
  beforeUnmount() {
    // 调用父组件的beforeUnmount方法
    if (this.$options.parent.beforeUnmount) {
      this.$options.parent.beforeUnmount.call(this);
    }
    // 清理当前组件创建的标记
    if (this.map && this.map.getOverlays) {
      const overlays = this.map.getOverlays();
      overlays.forEach(overlay => {
        if (overlay instanceof T.Marker) {
          this.map.removeOverLay(overlay);
        }
      });
    }
  },
  watch: {
    // 监听点位数据变化
    points: {
      handler() {
        // 确保地图初始化完成后才处理点位
        if (this.map && this.map.centerAndZoom) {
          this.addMarkers().catch(error => {
            console.error('添加标记失败:', error);
          });
        }
      },
      deep: true
    },
    // 地图实例监听
    map: {
      handler(newVal) {
        if (newVal && newVal.centerAndZoom) {
          // 延迟一点时间确保地图完全初始化
          this.$nextTick(() => {
            this.addMarkers().catch(error => {
              console.error('添加标记失败:', error);
            });
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    // 确保地图完全加载后再添加标记
    ensureMapReady() {
      return new Promise((resolve) => {
        if (this.map && this.map.centerAndZoom && typeof T !== 'undefined') {
          resolve();
        } else {
          // 等待地图初始化
          const checkMap = () => {
            if (this.map && this.map.centerAndZoom && typeof T !== 'undefined') {
              resolve();
            } else {
              setTimeout(checkMap, 100);
            }
          };
          checkMap();
        }
      });
    },

    async addMarkers() {
       try {
         // 确保地图完全加载
         await this.ensureMapReady();

         // 清除现有标记
         this.clearMarkers();

         // 验证点位数据
         if (!Array.isArray(this.points)) {
           this.$emit('map-loaded');
           return;
         }

        // 添加新标记
        const coordinates = []; // 收集所有有效坐标
        this.points.forEach((point, index) => {
          // 验证点位坐标
          if (typeof point.longitude !== 'number' || typeof point.latitude !== 'number') {
            console.warn('点位坐标无效:', point);
            return;
          }

          // 收集有效坐标
          coordinates.push({
            longitude: point.longitude,
            latitude: point.latitude
          });

          // 创建标记
          if (point.longitude < -180 || point.longitude > 180 ||
            point.latitude < -90 || point.latitude > 90) {
            return;
          }

          // 使用与TianDiTuLocationPicker相同的创建方式
          const marker = new T.Marker(new T.LngLat(point.longitude, point.latitude));
          this.map.addOverLay(marker);

          // 添加点击事件
          if (point.name) {
            marker.addEventListener("click", () => {
              const infoWindow = new T.InfoWindow(point.name);
              this.map.openInfoWindow(infoWindow, marker.getLngLat());
            });
          }
        });

         // 调整视野以显示所有坐标
         if (coordinates.length > 0) {
           try {
             // 计算中心点
             const totalLongitude = coordinates.reduce((sum, point) => sum + point.longitude, 0);
             const totalLatitude = coordinates.reduce((sum, point) => sum + point.latitude, 0);
             const centerLongitude = totalLongitude / coordinates.length;
             const centerLatitude = totalLatitude / coordinates.length;
             // 计算合适的缩放级别
             const zoom = this.calculateOptimalZoom(coordinates);
             // 确保坐标有效后再设置
             if (!isNaN(centerLongitude) && !isNaN(centerLatitude)) {
               this.map.centerAndZoom(new T.LngLat(centerLongitude, centerLatitude), zoom);
             } else {
               // 如果计算的中心点无效，使用第一个有效坐标
               const firstPoint = coordinates[0];
               if (firstPoint && !isNaN(firstPoint.longitude) && !isNaN(firstPoint.latitude)) {
                 this.map.centerAndZoom(new T.LngLat(firstPoint.longitude, firstPoint.latitude), zoom);
               }
             }
           } catch (error) {
             // 使用默认位置作为备选
             this.map.centerAndZoom(new T.LngLat(116.3972, 39.9075), 12);
           }
         } else {
           // 当没有坐标时，显示默认位置
           this.map.centerAndZoom(new T.LngLat(116.3972, 39.9075), 12);
         }
             } catch (error) {
         // 触发错误事件
         this.$emit('map-error', error);
       } finally {
        this.$nextTick(() => {
          this.$emit('map-loaded');
        });
      }
    },

    // 清除标记
    clearMarkers() {
       if (this.map && this.map.getOverlays) {
         try {
           const overlays = this.map.getOverlays();
           if (overlays && overlays.length > 0) {
             overlays.forEach(overlay => {
               if (overlay && overlay instanceof T.Marker) {
                 this.map.removeOverLay(overlay);
               }
             });
           }
         } catch (error) {
           // 忽略清除错误
         }
       }
     },

    // 计算缩放级别
    calculateOptimalZoom(coordinates) {
       if (coordinates.length <= 1) {
         return 14; // 单个点位使用较近的缩放级别
       }

       // 计算坐标的经纬度范围
       const longitudes = coordinates.map(p => p.longitude);
       const latitudes = coordinates.map(p => p.latitude);

       const minLng = Math.min(...longitudes);
       const maxLng = Math.max(...longitudes);
       const minLat = Math.min(...latitudes);
       const maxLat = Math.max(...latitudes);

       // 计算经纬度跨度
       const lngSpan = maxLng - minLng;
       const latSpan = maxLat - minLat;
       const maxSpan = Math.max(lngSpan, latSpan);

       // 添加边距系数，确保所有点位都能完整显示
       const paddingFactor = 1.2; // 增加20%的边距
       const adjustedSpan = maxSpan * paddingFactor;

       // 根据调整后的跨度计算合适的缩放级别
       let zoom;

       if (adjustedSpan > 10) zoom = 4;      // 跨度大，使用小缩放
       else if (adjustedSpan > 5) zoom = 5;
       else if (adjustedSpan > 2) zoom = 6;
       else if (adjustedSpan > 1) zoom = 7;
       else if (adjustedSpan > 0.5) zoom = 8;
       else if (adjustedSpan > 0.2) zoom = 9;
       else if (adjustedSpan > 0.1) zoom = 10;
       else if (adjustedSpan > 0.05) zoom = 11;
       else if (adjustedSpan > 0.02) zoom = 12;
       else if (adjustedSpan > 0.01) zoom = 13;
       else zoom = 14; // 默认使用较近的缩放级别
       // 确保缩放级别在合理范围内，并进一步减小以确保完整显示
       zoom = Math.max(4, Math.min(15, zoom));
       // 对于少量点位，进一步减小缩放级别以确保完整显示
       if (coordinates.length <= 3) {
         zoom = Math.max(4, zoom - 1);
       }
       return zoom;
     }
  }
}
</script>

<style scoped>
.tian-di-tu-map {
  width: 100%;
  height: 100%;
}
</style>
