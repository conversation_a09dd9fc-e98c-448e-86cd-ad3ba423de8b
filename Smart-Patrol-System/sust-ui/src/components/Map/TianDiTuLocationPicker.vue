<template>
  <div class="tian-di-tu-map" ref="mapContainer"></div>
</template>

<script>
import TianDiTuMap from './TianDiTuMap.vue';

export default {
  // 继承基础地图组件
  extends: TianDiTuMap,
  name: 'TianDiTuLocationPicker',
  props: {
    // 初始中心点坐标 [经度, 纬度]
    initialCenter: {
      type: Array,
      default: () => [116.39748, 39.90882] // 默认北京坐标
    },
    // 初始缩放级别
    initialZoom: {
      type: Number,
      default: 12
    },
    // 初始位置坐标
    initialLocation: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedLocation: null,
      currentMarker: null
    }
  },
  watch: {
    // 地图实例监听
    map: {
      handler(newVal) {
        if (newVal) {
          this.setupMapEvents();
          this.setInitialLocation();
          this.$emit('map-loaded');
        }
      }
    },
    // 监听初始位置变化
    initialLocation: {
      handler(newVal) {

        if (this.map && newVal) {
          this.setInitialLocationWithoutCentering();
        }
      },
      deep: true
    }
  },
  methods: {
    setupMapEvents() {
      try {
        if (this.map) {
          // 延迟设置单击事件，确保地图完全初始化
          setTimeout(() => {
            try {
              // 使用单击事件来选择位置
              if (typeof this.map.addEventListener === 'function') {
                this.map.addEventListener("click", (e) => {
                  try {
                    if (e && e.lnglat) {
                      const lngLat = e.lnglat;
                      this.updateLocation(lngLat.lng, lngLat.lat);
                    }
                  } catch (error) {
                    console.error('地图单击事件处理错误:', error);
                  }
                });
              }
            } catch (error) {
              console.error('设置地图单击事件失败:', error);
            }
          }, 1000);
        }
      } catch (error) {
        console.error('设置地图事件失败:', error);
      }
    },
    
    updateLocation(lng, lat) {
      this.selectedLocation = { longitude: lng, latitude: lat };
      
      // 清除之前的标记
      this.clearMarkers();
      
      // 创建标记
      this.currentMarker = this.createMarker(lng, lat);
      this.map.addOverLay(this.currentMarker);
      
      // 触发位置选择事件
      this.$emit('location-selected', {
        longitude: lng,
        latitude: lat
      });
    },
    
    clearMarkers() {
      if (this.map && this.map.getOverlays) {
        try {
          const overlays = this.map.getOverlays();
          if (overlays && overlays.length > 0) {
            overlays.forEach(overlay => {
              if (overlay && typeof overlay.getLngLat === 'function') {
                this.map.removeOverLay(overlay);
              }
            });
          }
          // 额外清除当前标记
          if (this.currentMarker) {
              this.map.removeOverLay(this.currentMarker);
          }
        } catch (error) {
          console.error('清除标记时出错:', error);
        }
      }
      this.currentMarker = null;
    },
    
    // 创建标记
    createMarker(lng, lat) {
      const marker = new T.Marker(new T.LngLat(lng, lat));
      return marker;
    },
    
    // 设置初始位置
    setInitialLocation() {

      // 清除之前的标记
      this.clearMarkers();
      
      if (this.initialLocation && this.initialLocation.longitude && this.initialLocation.latitude) {
        const lng = this.initialLocation.longitude;
        const lat = this.initialLocation.latitude;
        
        this.selectedLocation = { longitude: lng, latitude: lat };
        
        // 居中到点位坐标
        this.map.centerAndZoom(new T.LngLat(lng, lat), this.initialZoom);
        
        // 添加初始标记
        this.currentMarker = this.createMarker(lng, lat);
        this.map.addOverLay(this.currentMarker);
      } else {
        // 如果没有初始位置，使用天安门广场坐标

        const defaultLng = 116.39748; // 天安门广场经度
        const defaultLat = 39.90882;  // 天安门广场纬度
        this.map.centerAndZoom(new T.LngLat(defaultLng, defaultLat), this.initialZoom);
        this.selectedLocation = null;
      }
    },

    // 设置初始位置但不居中地图
    setInitialLocationWithoutCentering() {
      // 清除之前的标记
      this.clearMarkers();
      
      if (this.initialLocation && this.initialLocation.longitude && this.initialLocation.latitude) {
        const lng = this.initialLocation.longitude;
        const lat = this.initialLocation.latitude;

        this.selectedLocation = { longitude: lng, latitude: lat };

        // 添加初始标记，但不自动居中地图
        this.currentMarker = this.createMarker(lng, lat);
        this.map.addOverLay(this.currentMarker);
      }
    },
    
    // 获取当前选择的位置
    getSelectedLocation() {
      return this.selectedLocation;
    }
  }
}
</script>

<style scoped>
.tian-di-tu-map {
  width: 100%;
  height: 100%;
}
</style>