<template>
  <div class="tian-di-tu-map" ref="mapContainer"></div>
</template>

<script>
export default {
  name: 'TianDiTuMap',
  props: {
    // 地图中心点坐标 [经度, 纬度]
    center: {
      type: Array,
      default: () => [116.39748, 39.90882] // 默认北京坐标
    },
    // 地图缩放级别
    zoom: {
      type: Number,
      default: 12
    },
    // 天地图API密钥
    tk: {
      type: String,
      required: true,
      default: 'd315d778b0f2e4771bcad97f4e68f066'
    },
  },
  data() {
    return {
      map: null
    }
  },
  mounted() {
    this.loadMapScript()
  },
  beforeUnmount() {
    // 组件销毁时清理地图实例
    if (this.map) {
      this.map.destroy()
      this.map = null
    }
  },
  methods: {
    // 动态加载天地图API
    loadMapScript() {
      const script = document.createElement('script')
      script.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${this.tk}&type=webgl`
      script.onload = () => this.initMap()
      document.body.appendChild(script)
    },
    // 初始化地图
    initMap() {

        // 创建地图实例，添加配置参数
        const mapOptions = {
          projection: 'EPSG:4326',
          dragging: true,
          touchZoom: true,
          scrollWheelZoom: true,
          doubleClickZoom: false
        };
        
        this.map = new T.Map(this.$refs.mapContainer, mapOptions);
        
        // 设置中心点和缩放级别
        this.map.centerAndZoom([this.center[0], this.center[1]], this.zoom);
      
        
        // 添加地图类型控件（如果存在）
        if (T.Control && T.Control.MapType) {
          this.map.addControl(new T.Control.MapType());
        }
        
        // 添加导航控件（如果存在）
        if (T.Control && T.Control.Navigation) {
          this.map.addControl(new T.Control.Navigation());
        }
    
        // 触发地图加载完成事件
        this.$emit('map-loaded');
    },
  }
}
</script>

<style scoped>
.tian-di-tu-map {
  width: 100%;
  height: 100%;
  min-height: 500px;
}
</style>