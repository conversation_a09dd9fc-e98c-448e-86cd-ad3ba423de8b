<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="点位名称" prop="pointName">
        <el-input
          v-model="queryParams.pointName"
          placeholder="请输入点位名称"
          clearable
          style="width: 180px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域" prop="areaId">
        <treeselect
          v-model="queryParams.areaId"
          :options="areaOptions"
          :normalizer="normalizer"
          placeholder="请选择区域"
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="信标" prop="beaconName">
        <el-input
          v-model="queryParams.beaconName"
          placeholder="请输入信标编号"
          clearable
          style="width: 180px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="图层" prop="locationLayer">
        <el-select v-model="queryParams.locationLayer" placeholder="请选择图层" clearable style="width: 180px">
          <el-option
            v-for="layer in layerOptions"
            :key="layer.value"
            :label="layer.label"
            :value="layer.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 180px">
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['patrol:point:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['patrol:point:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['patrol:point:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['patrol:point:export']"
        >导出</el-button>
      </el-col>
      <!-- 查看地图按钮 -->
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-map"
          size="mini"
          @click="handleViewMap"
          v-hasPermi="['patrol:point:view']"
        >查看地图</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pointList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="点位名称" align="center" prop="pointName" />
      <el-table-column label="区域名称" align="center" prop="areaId" :formatter="(row, column) => findAreaNameById(areaOptions,row.areaId)"/>
      <el-table-column label="信标编号" align="center" prop="beaconName" />
      <el-table-column label="经度" align="center" prop="longtitude" />
      <el-table-column label="纬度" align="center" prop="latitude" />
      <el-table-column label="x坐标" align="center" prop="locationX" />
      <el-table-column label="y坐标" align="center" prop="locationY" />
      <el-table-column label="图层" align="center" prop="locationLayer" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['patrol:point:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['patrol:point:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改巡检点位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <!-- 左侧列 -->
          <el-col :span="12">
            <el-form-item label="点位名称" prop="pointName">
              <el-input v-model="form.pointName" placeholder="请输入点位名称" />
            </el-form-item>
            <el-form-item label="区域" prop="areaId">
              <treeselect
                v-model="form.areaId"
                :options="areaOptions"
                :normalizer="normalizer"
                placeholder="请选择区域"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="信标" prop="beaconId">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-input
                  v-model="selectedBeaconName"
                  placeholder="请选择信标"
                  readonly
                  style="flex: 1"
                />
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleSelectBeacon"
                >
                  选择信标
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="图层" prop="locationLayer">
              <el-input-number
                v-model="form.locationLayer"
                :min="1"
                :max="5"
                :precision="0"
                controls-position="right"
                placeholder="请选择图层"
                style="width: 100%"
              />
            </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dict.label}}</el-radio>
                </el-radio-group>
            </el-form-item>
          </el-col>

          <!-- 右侧列 -->
          <el-col :span="12">
            <el-form-item label="位置选择">
              <el-button
                type="primary"
                icon="el-icon-map-location"
                @click="handleSelectLocation"
                class="location-select-btn"
              >
                选择位置
              </el-button>
            </el-form-item>
            <el-form-item label="经度" prop="longtitude">
              <el-input v-model="form.longtitude" placeholder="点击选择位置自动生成" readonly />
            </el-form-item>
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="form.latitude" placeholder="点击选择位置自动生成" readonly />
            </el-form-item>
            <el-form-item label="x坐标" prop="locationX">
              <el-input v-model="form.locationX" placeholder="请输入x坐标" />
            </el-form-item>
            <el-form-item label="y坐标" prop="locationY">
              <el-input v-model="form.locationY" placeholder="请输入y坐标" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">巡检点位项目信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddPatrolPointItem">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddItemFromTemplate">从模板中添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeletePatrolPointItem">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="patrolPointItemList" :row-class-name="rowPatrolPointItemIndex" @selection-change="handlePatrolPointItemSelectionChange" ref="patrolPointItem">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="150"/>
          <el-table-column label="检查项名称" prop="itemName" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.itemName" placeholder="请输入检查项名称" />
            </template>
          </el-table-column>
          <el-table-column label="检查项描述" prop="description" width="250">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.description"
                type="textarea"
                :rows="1"
                placeholder="请输入检查项描述"
              />
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.status" placeholder="请选择状态">
                <el-option
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
      <el-dialog :title="title" :visible.sync="openTemplateList" width="600px" append-to-body>
        <el-form :model="templateQueryParams" ref="templateQueryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model="templateQueryParams.templateName"
              placeholder="请输入模板名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="模板类型" prop="type">
            <el-input
              v-model="templateQueryParams.type"
              placeholder="请输入模板类型"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleTemplateQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetTemplateQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="templateList" @selection-change="(selection) => selectedTemplates = selection">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="模板名称" prop="templateName"></el-table-column>
          <el-table-column label="模板描述" prop="description"></el-table-column>
          <el-table-column label="模板类型" prop="type"></el-table-column>
        </el-table>
        <pagination
          v-show="totalTemplate>0"
          :total="totalTemplate"
          :page.sync="templateQueryParams.pageNum"
          :limit.sync="templateQueryParams.pageSize"
          @pagination="getTemplateList"
        />
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitTemplateSelection">确 定</el-button>
          <el-button @click="cancelTemplateSelection">取 消</el-button>
        </div>
      </el-dialog>
    </el-dialog>

      <!-- 位置选择对话框 -->
     <el-dialog
       title="选择位置"
       :visible.sync="locationPickerVisible"
       width="60%"
       height="70%"
       append-to-body
       :close-on-click-modal="false"
       @close="handleLocationPickerClose"
     >
       <div class="location-map-container">
         <tian-di-tu-location-picker
           :key="locationPickerKey"
           :tk="tianDiTuKey"
           :initial-center="defaultCenter"
           :initial-zoom="12"
           :initial-location="selectedLocation"
           @location-selected="handleLocationSelected"
           @map-loaded="handleMapLoaded"
         ></tian-di-tu-location-picker>
       </div>

       <!-- 坐标显示信息 -->
       <div class="location-info">
         <div class="coordinate-display">
           <div class="coordinate-item">
             <span class="label">经度:</span>
             <span class="value">{{ selectedLocation ? selectedLocation.longitude.toFixed(6) : '--' }}</span>
           </div>
           <div class="coordinate-item">
             <span class="label">纬度:</span>
             <span class="value">{{ selectedLocation ? selectedLocation.latitude.toFixed(6) : '--' }}</span>
           </div>
         </div>
         <div class="instruction">
           单击地图选择位置，经纬度将自动生成
         </div>
       </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="locationPickerVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmLocation">确认位置</el-button>
      </div>
    </el-dialog>

    <!-- 信标选择对话框 -->
    <el-dialog
      title="选择信标"
      :visible.sync="beaconDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="cancelBeaconSelection"
    >
      <el-form :model="beaconQueryParams" ref="beaconQueryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="信标名称" prop="beaconName">
          <el-input
            v-model="beaconQueryParams.beaconName"
            placeholder="请输入信标名称"
            clearable
            style="width: 180px"
            @keyup.enter.native="handleBeaconQuery"
          />
        </el-form-item>
        <el-form-item label="MAC" prop="beaconMac">
          <el-input
            v-model="beaconQueryParams.beaconMac"
            placeholder="请输入信标MAC"
            clearable
            style="width: 180px"
            @keyup.enter.native="handleBeaconQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleBeaconQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetBeaconQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="beaconDialogList"
        @row-click="handleBeaconRowClick"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="信标名称" align="center" prop="beaconName" />
        <el-table-column label="信标MAC" align="center" prop="beaconMac" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="confirmBeaconSelection(scope.row)"
            >选择</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="beaconDialogTotal>0"
        :total="beaconDialogTotal"
        :page.sync="beaconQueryParams.pageNum"
        :limit.sync="beaconQueryParams.pageSize"
        @pagination="getBeaconDialogList"
        :pagerCount="5"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBeaconSelection">取 消</el-button>
        <el-button type="primary" @click="confirmSelectedBeacon">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 查看地图对话框 -->
    <el-dialog
      title="点位地图视图"
      :visible.sync="mapDialogVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <!-- 地图统计信息 -->
      <div class="map-stats">
        <div class="stat-item">
          <span class="stat-number stat-display">{{ selectedPoints.length }}</span>
          <span class="stat-label">地图显示点位数量</span>
        </div>
        <div class="stat-item">
          <span class="stat-number stat-normal">{{ normalCount }}</span>
          <span class="stat-label">正常点位数量</span>
        </div>
        <div class="stat-item">
          <span class="stat-number stat-disabled">{{ disabledCount }}</span>
          <span class="stat-label">停用点位数量</span>
        </div>
        <div class="stat-item">
          <span class="stat-number stat-total">{{ pointList.length }}</span>
          <span class="stat-label">点位总数</span>
        </div>
      </div>

      <div class="map-container">
        <tian-di-tu-all-point-map
          :key="mapComponentKey"
          :tk="tianDiTuKey"
          :points="selectedPoints"
          @map-loaded="handleMapLoaded"
          @map-error="handleMapError"
        ></tian-di-tu-all-point-map>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listPoint, getPoint, delPoint, addPoint, updatePoint } from "@/api/patrol/point";
import { listTemplate } from "@/api/patrol/template";
import { areaTreeSelect } from "@/api/patrol/area";
import { listBeacon } from "@/api/beacon/beacon";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
// 查看地图功能组件
import TianDiTuAllPointMap from '@/components/Map/TianDiTuAllPointMap.vue';
// 位置选择地图组件
import TianDiTuLocationPicker from '@/components/Map/TianDiTuLocationPicker.vue';


export default {
  name: "Point",
  dicts: ['sys_normal_disable'],
  components: {
    TianDiTuAllPointMap, // 查看地图组件
    TianDiTuLocationPicker, // 位置选择组件
    Treeselect //树状图组件
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedPatrolPointItem: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 巡检点位表格数据
      pointList: [],
      // 巡检点位项目表格数据
      patrolPointItemList: [],
      selectedTemplates: [],
      templateList: [],
      totalTemplate: 0,
      templateQueryParams: { // 模板查询参数
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        type: null,
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openTemplateList: false,
      //地图界面
      mapDialogVisible: false,
      //地图加载
      mapLoading: false,
      //点位合集
      selectedPoints: [],
      //位置选择对话框
      locationPickerVisible: false,
      //位置选择加载
      locationPickerLoading: false,
      // 选中的位置
      selectedLocation: null,
      // 天地图API密钥
      tianDiTuKey: 'd315d778b0f2e4771bcad97f4e68f066',
      // 默认地图中心点 [经度, 纬度]
      defaultCenter: [116.39748, 39.90882],
      // 位置选择组件key
      locationPickerKey: Date.now(),
      // 查看地图组件key
      mapComponentKey: Date.now(),
      // 区域树选项
      areaOptions: [],
      // 图层选项
      layerOptions: [
        { value: 1, label: '图层1' },
        { value: 2, label: '图层2' },
        { value: 3, label: '图层3' },
        { value: 4, label: '图层4' },
        { value: 5, label: '图层5' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        pointName: null,
        areaId: null,
        beaconName: null,
        locationLayer: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        pointName: [
          { required: true, message: "点位名称不能为空", trigger: "blur" }
        ],
        areaId: [
          { required: true, message: "区域不能为空", trigger: "change" }
        ],
        beaconId: [
          { required: true, message: "信标不能为空", trigger: "blur" }
        ],
        longtitude: [
          { required: true, message: "请选择位置", trigger: "change" }
        ],
        latitude: [
          { required: true, message: "请选择位置", trigger: "change" }
        ],
        locationX: [
          { required: true, message: "x坐标不能为空", trigger: "blur" }
        ],
        locationY: [
          { required: true, message: "y坐标不能为空", trigger: "blur" }
        ],
        locationLayer: [
          { required: true, message: "图层不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "逻辑删除不能为空", trigger: "blur" }
        ]
      },
      // 信标选择对话框
      beaconDialogVisible: false,
      // 选中的信标名称
      selectedBeaconName: '',
      // 选中的信标ID
      selectedBeaconId: null,
      // 信标选择对话框列表数据
      beaconDialogList: [],
      beaconDialogTotal: 0,
      beaconQueryParams: { // 信标查询参数
        pageNum: 1,
        pageSize: 10,
        beaconName: null,
        beaconMac: null,
      },
      selectedBeaconForDialog: null, // 用于存储选中的信标
    };
  },
  computed: {
    /** 正常点位数量 */
    normalCount() {
      return this.pointList.filter(p => p.status === '0').length;
    },
    /** 停用点位数量 */
    disabledCount() {
      return this.pointList.filter(p => p.status === '1').length;
    }
  },
  created() {
    this.getList();
    this.getAreaTree();
  },
  watch: {
    pointList: {
      handler(newVal, oldVal) {
        if (this.mapDialogVisible && newVal !== oldVal) {
          this.refreshMapData();
        }
      },
      deep: true
    }
  },
  methods: {
    /** 查询巡检点位列表 */
    getList() {
      this.loading = true;
      listPoint(this.queryParams).then(response => {
        this.pointList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询区域树结构 */
    getAreaTree() {
      areaTreeSelect().then(response => {
        this.areaOptions = response.data;
      });
    },
    /** 根据区域ID查找区域名称 */
    findAreaNameById(treeData, id) {
      if(!treeData)
        return;
      for (const node of treeData) {
        if (node.id === id) {
          return node.label; // 找到匹配项，返回区域名称
        }
        if (node.children && node.children.length) {
          const found = this.findAreaNameById(node.children, id); // 递归查找子节点
          if (found) return found; // 如果在子节点中找到，返回结果
        }
      }
      return null; // 未找到
    },
    /** 树形数据转换器 */
    normalizer(node) {
      return {
        id: node.id,
        label: node.label,
        children: node.children
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        pointId: null,
        pointName: null,
        areaId: null,
        beaconId: null,
        longtitude: null,
        latitude: null,
        locationX: null,
        locationY: null,
        locationLayer: null,
        status: "0"
      };
      this.patrolPointItemList = [];
      this.selectedTemplates = [];
      this.selectedLocation = null;
      this.selectedBeaconName = '';
      this.selectedBeaconId = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.pointId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加巡检点位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const pointId = row.pointId || this.ids
      getPoint(pointId).then(response => {
        this.form = response.data;
        this.patrolPointItemList = response.data.patrolPointItemList;

        // 如果有经纬度数据，设置选中的位置
        if (this.form.longtitude && this.form.latitude) {
          this.selectedLocation = {
            longitude: Number(this.form.longtitude),
            latitude: Number(this.form.latitude)
          };
        } else {
          this.selectedLocation = null;
        }

        // 如果有信标数据，设置选中的信标名称
        if (this.form.beaconId) {
          this.selectedBeaconName = this.form.beaconName || '';
          this.selectedBeaconId = this.form.beaconId;
        } else {
          this.selectedBeaconName = '';
          this.selectedBeaconId = null;
        }

        this.open = true;
        this.title = "修改巡检点位";
      });
    },
    /** 查看地图按钮操作 */
    handleViewMap() {
      // 验证是否有权限和数据
      if (!this.pointList || this.pointList.length === 0) {
        this.$modal.msgWarning('没有可显示的点位数据');
        return;
      }

      // 每次打开地图时重新获取最新数据
      this.refreshMapData();
    },

    /** 刷新地图数据 */
    refreshMapData() {
      // 重新获取所有点位数据，过滤掉状态为停用的点位（status为1表示停用）
      this.selectedPoints = this.pointList
        .filter(point => point.status !== '1') // 过滤掉停用的点位
        .map(this.transformPointData)
        .filter(point => point !== null);

      // 检查是否有有效的点位数据
      if (this.selectedPoints.length === 0) {
        this.$modal.msgWarning('没有可显示的有效点位数据');
        return;
      }

      // 更新查看地图组件key，确保重新初始化
      this.mapComponentKey = Date.now();

      // 使用$nextTick确保数据更新后再显示地图
      this.$nextTick(() => {
        // 显示地图对话框
        this.mapDialogVisible = true;
        this.mapLoading = true;
      });
    },

    /** 选择位置按钮操作 */
    handleSelectLocation() {
      // 重置选中位置
      this.selectedLocation = null;

      // 更新key值，强制创建新组件
      this.locationPickerKey = Date.now();

      // 如果有现有坐标，设置为初始位置
      if (this.form.longtitude && this.form.latitude) {
        this.selectedLocation = {
          longitude: Number(this.form.longtitude),
          latitude: Number(this.form.latitude)
        };
      }

      this.locationPickerVisible = true;
      this.locationPickerLoading = true;

      // 设置超时保护
      setTimeout(() => {
        this.locationPickerLoading = false;
      }, 2000);
    },

    /** 处理位置选择事件 */
    handleLocationSelected(location) {
      this.selectedLocation = location;
    },

    /** 处理地图加载完成事件 */
    handleMapLoaded() {
      this.locationPickerLoading = false;
    },

    /** 备用加载完成处理 */
    handleLocationPickerClose() {
      // 如果地图加载超时，强制关闭加载状态
      setTimeout(() => {
        this.locationPickerLoading = false;
      }, 1000);
    },

    /** 确认位置选择 */
    confirmLocation() {
      if (this.selectedLocation) {
        this.form.longtitude = this.selectedLocation.longitude.toFixed(6);
        this.form.latitude = this.selectedLocation.latitude.toFixed(6);
        this.locationPickerVisible = false;
        this.$modal.msgSuccess("位置选择成功");
      } else {
        this.$modal.msgWarning("请先在地图上选择位置");
      }
    },

    /** 处理地图错误 */
    handleMapError(error) {
      this.mapLoading = false;
      this.$modal.msgError("地图加载失败，请检查网络连接或稍后重试");
      console.error('地图错误:', error);
    },

    // 数据转换方法
    transformPointData(point) {
      // 验证并转换坐标数据
      const longitude = Number(point.longtitude);
      const latitude = Number(point.latitude);
      const result = {
        ...point,
        longitude,
        latitude,
        // 确保名称字段存在
        name: point.pointName || `点位${point.pointId}`
      };

      return result;
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.patrolPointItemList = this.patrolPointItemList;
          if (this.form.pointId != null) {
            updatePoint(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPoint(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const pointIds = row.pointId || this.ids;
      this.$modal.confirm('是否确认删除巡检点位编号为"' + pointIds + '"的数据项？').then(function() {
        return delPoint(pointIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 巡检点位项目序号 */
    rowPatrolPointItemIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 巡检点位项目添加按钮操作 */
    handleAddPatrolPointItem() {
      let obj = {};
      obj.itemName = "";
      obj.description = "";
      obj.status = "";
      this.patrolPointItemList.push(obj);
    },
    getTemplateList() {
      listTemplate(this.templateQueryParams).then(res => {
        this.templateList = res.rows;
        this.totalTemplate = res.total;
      });
    },
    resetTemplateQuery() {
      this.resetForm("templateQueryForm");
      this.handleTemplateQuery();
    },
    handleTemplateQuery() {
      this.templateQueryParams.pageNum = 1;
      this.getTemplateList();
    },
    handleAddItemFromTemplate() {
      this.selectedTemplates = []
      this.getTemplateList()
      this.openTemplateList = true
    },
    submitTemplateSelection() {
      for(let template of this.selectedTemplates) {
        let obj = {};
        obj.itemName = template.templateName;
        obj.description = template.description;
        obj.status = '0';
        this.patrolPointItemList.push(obj);
      }
      this.openTemplateList = false
      this.selectedTemplates = []
    },
    cancelTemplateSelection() {
      this.openTemplateList = false
      this.selectedTemplates = []
    },
    /** 巡检点位检查项删除按钮操作 */
    handleDeletePatrolPointItem() {
      if (this.checkedPatrolPointItem.length == 0) {
        this.$modal.msgError("请先选择要删除的巡检点位检查项数据");
      } else {
        const patrolPointItemList = this.patrolPointItemList;
        const checkedPatrolPointItem = this.checkedPatrolPointItem;
        this.patrolPointItemList = patrolPointItemList.filter(function(item) {
          return checkedPatrolPointItem.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handlePatrolPointItemSelectionChange(selection) {
      this.checkedPatrolPointItem = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('patrol/point/export', {
        ...this.queryParams
      }, `point_${new Date().getTime()}.xlsx`)
    },
    /** 选择信标按钮操作 */
    handleSelectBeacon() {
      this.beaconDialogVisible = true;
      // 从信标选择对话框列表中查找信标名称
      if (this.form.beaconId) {
        const beacon = this.beaconDialogList.find(b => b.beaconId === this.form.beaconId);
        this.selectedBeaconName = beacon ? beacon.beaconName : '';
      } else {
        this.selectedBeaconName = '';
      }
      this.selectedBeaconId = this.form.beaconId;
      // 重置查询参数并加载信标列表，只显示状态正常的信标
      this.beaconQueryParams = {
        pageNum: 1,
        pageSize: 10,
        beaconName: null,
        beaconMac: null,
      };
      this.selectedBeaconForDialog = null;
      this.getBeaconDialogList();
    },
    /** 信标选择对话框确认 */
    confirmBeaconSelection(beacon) {
      this.form.beaconId = beacon.beaconId;
      this.selectedBeaconName = beacon.beaconName;
      this.selectedBeaconId = beacon.beaconId;
      this.beaconDialogVisible = false;
      this.selectedBeaconForDialog = null;
      this.$modal.msgSuccess("信标选择成功");
    },
    /** 信标选择对话框取消 */
    cancelBeaconSelection() {
      this.beaconDialogVisible = false;
      // 从信标选择对话框列表中查找信标名称
      if (this.form.beaconId) {
        const beacon = this.beaconDialogList.find(b => b.beaconId === this.form.beaconId);
        this.selectedBeaconName = beacon ? beacon.beaconName : '';
      } else {
        this.selectedBeaconName = '';
      }
      this.selectedBeaconId = this.form.beaconId;
      this.selectedBeaconForDialog = null;
    },
    /** 信标选择对话框查询 */
    handleBeaconQuery() {
      this.beaconQueryParams.pageNum = 1;
      this.getBeaconDialogList();
    },
    /** 信标选择对话框重置 */
    resetBeaconQuery() {
      this.resetForm("beaconQueryForm");
      this.handleBeaconQuery();
    },
    /** 获取信标选择对话框列表 */
    getBeaconDialogList() {
      // 构建查询参数，确保只获取状态正常的信标
      const queryParams = {
        ...this.beaconQueryParams,
        status: '0' // 只显示状态正常的信标
      };
      listBeacon(queryParams).then(res => {
        this.beaconDialogList = res.rows;
        this.beaconDialogTotal = res.total;
      });
    },
    /** 信标选择对话框行点击 */
    handleBeaconRowClick(row) {
      this.selectedBeaconForDialog = row;
    },
    /** 信标选择对话框确认选中 */
    confirmSelectedBeacon() {
      if (this.selectedBeaconForDialog) {
        this.confirmBeaconSelection(this.selectedBeaconForDialog);
      } else {
        this.$modal.msgWarning("请先点击选择一个信标");
      }
    },

  }
};
</script>

<style scoped>
/* 地图样式 */
.map-stats {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-display {
  color: #1890ff;
}

.stat-normal {
  color: #52c41a;
}

.stat-disabled {
  color: #faad14;
}

.stat-total {
  color: #666;
}

.stat-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  line-height: 1;
}

/* 地图容器样式 */
.map-container {
  height: 500px;
}

/* 位置选择地图容器样式 */
.location-map-container {
  height: 400px;
}

/* 位置信息样式 */
.location-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.coordinate-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 8px;
}

.coordinate-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.coordinate-item .label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.coordinate-item .value {
  font-family: 'Courier New', monospace;
  background-color: #fff;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  font-size: 13px;
  color: #495057;
  min-width: 100px;
  text-align: center;
}

.instruction {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  font-style: italic;
}

/* 位置选择按钮样式 */
.location-select-btn {
  width: 100%;
}

/* 信标选择对话框样式 */
.beacon-selection-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.beacon-input {
  flex: 1;
}

.beacon-button {
  white-space: nowrap;
}
</style>
