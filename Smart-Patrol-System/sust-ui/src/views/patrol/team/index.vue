<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧部门树菜单 -->
      <el-col :span="4">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-office-building"></i> 部门结构</span>
          </div>
          <div class="head-container">
            <el-input
              v-model="deptName"
              placeholder="输入部门名称"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
            />
          </div>
          <div class="head-container">
            <el-tree
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="deptTree"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧主内容区域 -->
      <el-col :span="20">
        <!-- 搜索区域 -->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
          <el-form-item label="巡逻队伍名称" prop="teamName">
            <el-input
              v-model="queryParams.teamName"
              placeholder="输入队伍名称"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="巡逻队伍状态" clearable style="width: 240px">
              <el-option
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作按钮区域 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['patrol:team:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['patrol:team:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['patrol:team:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['patrol:team:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 数据表格区域 -->
        <el-table v-loading="loading" :data="teamList" @selection-change="handleSelectionChange" border stripe>
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="巡逻队伍名称" align="center" prop="teamName" :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column label="所属部门" align="center" prop="deptName" :show-overflow-tooltip="true">
            <template v-slot="scope">
              <i class="el-icon-office-building" style="margin-right: 5px; color: #67C23A;"></i>
              {{ scope.row.deptName }}
            </template>
          </el-table-column>
          <el-table-column label="成员数量" align="center" prop="memberCount" width="120">
            <template v-slot="scope">
              <el-tag size="mini" type="info">
                <i class="el-icon-user" style="margin-right: 3px; color: #409eff"></i>
                {{ scope.row.memberCount || 0 }}人
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template v-slot="scope">
              <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
            <template v-slot="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['patrol:team:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['patrol:team:remove']"
              >删除</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleViewMembers(scope.row)"
                v-hasPermi="['patrol:team:query']"
              >查看成员</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改巡逻队伍对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-card class="box-card" shadow="never" style="margin-bottom: 20px;" >
              <div slot="header" class="clearfix">
                <span><i class="el-icon-info"></i> 基本信息</span>
              </div>
              <el-row :gutter="20">
                <el-col :span="10">
                  <el-form-item label="队伍名称" prop="teamName">
                    <el-input
                      v-model="form.teamName"
                      placeholder="输入巡逻队伍名称"
                      maxlength="30"
                      prefix-icon="el-icon-user-solid"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="所属部门" prop="deptId">
                    <treeselect
                      v-model="form.deptId"
                      :options="enabledDeptOptions"
                      :show-count="true"
                      :max-height="100"
                      :scrollable="true"
                      placeholder="选择所属部门"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="队伍状态">
                    <el-radio-group v-model="form.status">
                      <el-radio
                        v-for="dict in dict.type.sys_normal_disable"
                        :key="dict.value"
                        :label="dict.value"
                      >{{ dict.label }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="box-card" shadow="never">
              <div slot="header" class="clearfix">
                <span><i class="el-icon-user"></i> 巡逻队伍成员</span>
                <div style="float: right;">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="openMemberSelector(form)"
                  >添加成员</el-button>
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="selectedTeamMembers.length === 0"
                    @click="handleBatchRemoveMembers"
                  >删除</el-button>
                </div>
              </div>

              <el-table
                v-if="form.memberIds && form.memberIds.length > 0"
                :data="selectedMemberDetails"
                style="width: 100%"
                max-height="300"
                :scrollable="true"
                size="small"
                border
                stripe
                @selection-change="handleTeamMemberSelectionChange"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column label="姓名" prop="userName" align="center">
                  <template v-slot="scope">
                    <i class="el-icon-user-solid" style="margin-right: 5px; color: #409EFF;"></i>
                    {{ scope.row.userName }}
                  </template>
                </el-table-column>
                <el-table-column label="昵称" prop="nickName" align="center" />
                <el-table-column label="性别" width="80" align="center">
                  <template v-slot="scope">
                    <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.sex"/>
                  </template>
                </el-table-column>
                <el-table-column label="手机号码" prop="phonenumber" align="center" />
                <el-table-column label="状态" width="80" align="center">
                  <template v-slot="scope">
                    <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                  </template>
                </el-table-column>
                <el-table-column label="所属部门" prop="dept.deptName" align="center">
                  <template v-slot="scope">
                    <i class="el-icon-office-building" style="margin-right: 5px; color: #67C23A;"></i>
                    {{ scope.row.dept.deptName }}
                  </template>
                </el-table-column>
              </el-table>

              <div v-else class="empty-state">
                <i class="el-icon-user" style="font-size: 48px; color: #C0C4CC; display: block; margin-bottom: 16px;"></i>
                <p style="color: #909399; margin: 0;">暂无成员，请点击"添加成员"按钮添加巡逻队伍成员</p>
              </div>
            </el-card>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 查看成员对话框 -->
        <el-dialog title="巡逻队伍成员详情" :visible.sync="memberOpen" width="1200px" append-to-body>
          <div style="margin-bottom: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;">
            <i class="el-icon-info" style="color: #409EFF; margin-right: 8px;"></i>
            <span style="font-weight: 500;">成员总数：</span>
            <el-tag type="primary" size="small">{{ memberList.length }} 人</el-tag>
          </div>

          <el-table :data="memberList" style="width: 100%" border stripe max-height="400" :scrollable="true">
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="姓名" prop="userName" align="center">
              <template v-slot="scope">
                <i class="el-icon-user-solid" style="margin-right: 5px; color: #409EFF;"></i>
                {{ scope.row.userName }}
              </template>
            </el-table-column>
            <el-table-column label="昵称" prop="nickName" align="center" />
            <el-table-column label="性别" prop="sex" width="80" align="center">
              <template v-slot="scope">
                <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.sex"/>
              </template>
            </el-table-column>
            <el-table-column label="手机号码" prop="phonenumber" align="center" />
            <el-table-column label="邮箱" prop="email" align="center" :show-overflow-tooltip="true" />
            <el-table-column label="状态" width="80" align="center">
              <template v-slot="scope">
                <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
              </template>
            </el-table-column>
            <el-table-column label="所属部门" prop="dept.deptName" align="center">
              <template v-slot="scope">
                <i class="el-icon-office-building" style="margin-right: 5px; color: #67C23A;"></i>
                {{ scope.row.dept.deptName }}
              </template>
            </el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="memberOpen = false">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- 选择成员对话框 -->
        <el-dialog title="选择巡逻队伍成员" :visible.sync="memberSelectorOpen" width="1200px" append-to-body>
          <!-- 搜索条件 -->
          <el-card class="box-card" shadow="never" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
              <span><i class="el-icon-search"></i> 搜索条件</span>
            </div>
            <el-form :model="memberQueryParams" ref="memberQueryForm" size="small" :inline="true" label-width="80px">
              <el-form-item label="姓名" prop="userName">
                <el-input
                  v-model="memberQueryParams.userName"
                  placeholder="输入用户姓名"
                  clearable
                  style="width: 200px"
                  prefix-icon="el-icon-user"
                  @keyup.enter.native="handleMemberQuery"
                />
              </el-form-item>
              <el-form-item label="昵称" prop="nickName">
                <el-input
                  v-model="memberQueryParams.nickName"
                  placeholder="输入昵称"
                  clearable
                  style="width: 200px"
                  prefix-icon="el-icon-user"
                  @keyup.enter.native="handleMemberQuery"
                />
              </el-form-item>
              <el-form-item label="手机号码" prop="phonenumber">
                <el-input
                  v-model="memberQueryParams.phonenumber"
                  placeholder="输入手机号码"
                  clearable
                  style="width: 200px"
                  prefix-icon="el-icon-phone"
                  @keyup.enter.native="handleMemberQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleMemberQuery(form)">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetMemberQuery(form)">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 用户列表 -->
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span><i class="el-icon-user"></i> 候选成员列表</span>
              <div style="float: right;">
                <el-tag type="info" size="small" v-if="memberTotal > 0">共 {{ memberTotal }} 人</el-tag>
              </div>
            </div>

            <el-table
              v-loading="memberLoading"
              :data="memberCandidateList"
              @selection-change="handleMemberSelectionChange"
              border
              stripe
            >
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column label="姓名" align="center" prop="userName">
                <template v-slot="scope">
                  <i class="el-icon-user-solid" style="margin-right: 5px; color: #409EFF;"></i>
                  {{ scope.row.userName }}
                </template>
              </el-table-column>
              <el-table-column label="昵称" align="center" prop="nickName" />
              <el-table-column label="所属部门" align="center" prop="dept.deptName">
                <template v-slot="scope">
                  <i class="el-icon-office-building" style="margin-right: 5px; color: #67C23A;"></i>
                  {{ scope.row.dept.deptName }}
                </template>
              </el-table-column>
              <el-table-column label="手机号码" align="center" prop="phonenumber" />
              <el-table-column label="性别" prop="sex" width="80" align="center">
                <template v-slot="scope">
                  <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.sex"/>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="80" align="center">
                <template v-slot="scope">
                  <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div style="margin-top: 20px; text-align: center;">
              <pagination
                v-show="memberTotal > 0"
                :total="memberTotal"
                :page.sync="memberQueryParams.pageNum"
                :limit.sync="memberQueryParams.pageSize"
                @pagination="getMemberCandidateList"
              />
            </div>
          </el-card>

          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirmSelectMembers" icon="el-icon-check">确定选择</el-button>
            <el-button @click="memberSelectorOpen = false">取 消</el-button>
          </div>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listTeam, getTeam, delTeam, addTeam, updateTeam, getTeamUserList, checkTeamName } from "@/api/patrol/team";
import { deptTreeSelect} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Team",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 提交加载状态
      submitLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 巡逻队伍表格数据
      teamList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        teamName: null,
        deptId: null,
        status: null,
      },
      deptOptions: undefined,
      // 过滤掉已禁用部门树选项
      enabledDeptOptions: undefined,

      // 表单参数
      form: {
        memberIds: []
      },
      // 表单校验
      rules: {
        teamName: [
          { required: true, message: "巡逻队名称不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      },
      // 用户选项
      userOptions: [],
      // 成员列表
      memberList: [],
      // 查看成员对话框
      memberOpen: false,
      // 选择成员对话框
      memberSelectorOpen: false,
      // 选择成员的查询参数
      memberQueryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        nickName: null,
        deptId: null,
        phonenumber: null,
        status: 0,
      },
      // 选择成员的加载状态
      memberLoading: false,
      // 选择成员的候选列表
      memberCandidateList: [],
      // 选择成员的总条数
      memberTotal: 0,

      // 用于存储选中的团队成员
      selectedTeamMembers: [],

      // 部门树相关数据
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 部门搜索名称
      deptName: '',
    };
  },
  created() {
    this.getList();
    this.getUserOptions();
    this.getDeptTree();
  },
  computed: {
    selectedMemberDetails() {
      return this.userOptions.filter(user => this.form.memberIds && this.form.memberIds.includes(user.userId));
    }
  },
  watch: {
    // 监听部门名称搜索
    deptName(val) {
      this.$refs.deptTree.filter(val);
    }
  },
  methods: {
    /** 查询巡逻队伍列表 */
    getList() {
      this.loading = true;
      listTeam(this.queryParams).then(response => {
        this.teamList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
        this.enabledDeptOptions = this.filterDisabledDept(JSON.parse(JSON.stringify(response.data)));
      });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter(dept => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },

    // 部门树过滤方法
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 部门树节点点击事件
    handleNodeClick(data) {
      // 设置查询参数中的部门ID
      this.queryParams.deptId = data.id;
      // 重新查询数据
      this.handleQuery();
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        teamId: null,
        teamName: null,
        deptId: null,
        status: null,
        memberIds: [],
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.teamId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 设置新增时的默认状态为正常
      this.form.status = "0";
      this.open = true;
      this.title = "添加巡逻队伍";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const teamId = row.teamId || this.ids
      getTeam(teamId).then(response => {
        this.form = response.data;
        this.form.memberIds = this.form.memberIds || [];
        this.open = true;
        this.title = "修改巡逻队伍";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 先检查队伍名称是否重复
          this.checkTeamNameDuplicate().then(isDuplicate => {
            if (isDuplicate) {
              this.$modal.msgWarning(`队伍名称"${this.form.teamName}"在当前部门下已存在，请修改后重试！`);
              return;
            }

            // 名称不重复，继续提交
            this.submitLoading = true;
            if (this.form.teamId != null) {
              updateTeam(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).finally(() => {
                this.submitLoading = false;
              });
            } else {
              addTeam(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).finally(() => {
                this.submitLoading = false;
              });
            }
          }).catch(() => {
            // 检查失败，不进行提交
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const teamIds = row.teamId || this.ids;
      this.$modal.confirm('是否确认删除巡逻队伍编号为"' + teamIds + '"的数据项？').then(function() {
        return delTeam(teamIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('patrol/team/export', {
        ...this.queryParams
      }, `team_${new Date().getTime()}.xlsx`)
    },
    /** 获取用户选项 */
    getUserOptions() {
      getTeamUserList().then(response => {
        this.userOptions = response.rows;
      });
    },
    /** 查看成员 */
    handleViewMembers(row) {
      getTeam(row.teamId).then(response => {
        this.memberList = response.data.members || [];
        this.memberOpen = true;
      });
    },
    /** 打开选择成员对话框 */
    openMemberSelector(data) {
      this.memberSelectorOpen = true;
      this.getMemberCandidateList(data);
    },
    /** 查询成员候选列表 */
    getMemberCandidateList(data) {
      this.memberLoading = true;
      this.memberQueryParams.deptId = data.deptId;
      getTeamUserList(this.memberQueryParams).then(response => {
        // 过滤掉已经添加的成员
        const existingMemberIds = this.form.memberIds || [];
        this.memberCandidateList = response.rows.filter(user =>
          !existingMemberIds.includes(user.userId)
        );
        this.memberTotal = this.memberCandidateList.length;
        this.memberLoading = false;
      });
    },
    /** 搜索成员 */
    handleMemberQuery(data) {
      this.memberQueryParams.pageNum = 1;
      this.getMemberCandidateList(data);
    },
    /** 重置成员搜索 */
    resetMemberQuery(data) {
      this.resetForm("memberQueryForm");
      this.handleMemberQuery(data);
    },
    /** 处理成员选择变化 */
    handleMemberSelectionChange(selection) {
      this.selectedMembers = selection;
    },
    /** 确认选择成员 */
    confirmSelectMembers() {
      const newMemberIds = this.selectedMembers.map(user => user.userId);
      // 合并已有成员和新选择的成员，去重
      const allMemberIds = [...new Set([...this.form.memberIds, ...newMemberIds])];
      this.form.memberIds = allMemberIds;

      // 从候选列表中移除已选择的成员
      this.memberCandidateList = this.memberCandidateList.filter(user =>
        !newMemberIds.includes(user.userId)
      );
      this.memberTotal = this.memberCandidateList.length;

      this.memberSelectorOpen = false;
      this.selectedMembers = [];
    },


    /** 处理团队成员选择变化 */
    handleTeamMemberSelectionChange(selection) {
      this.selectedTeamMembers = selection;
    },

    /** 批量移除成员 */
    handleBatchRemoveMembers() {
      this.$modal.confirm('是否确认删除选中的' + this.selectedTeamMembers.length + '名成员？').then(() => {
        const userIds = this.selectedTeamMembers.map(member => member.userId);
        // 从成员ID列表中移除选中的用户
        this.form.memberIds = this.form.memberIds.filter(id => !userIds.includes(id));
        this.selectedTeamMembers = [];

        // 如果成员选择对话框是打开的，需要重新获取候选列表以包含被移除的成员
        if (this.memberSelectorOpen) {
          this.getMemberCandidateList(this.form);
        }

        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 检查队伍名称是否重复 */
    checkTeamNameDuplicate() {
      return new Promise((resolve, reject) => {
        if (!this.form.teamName || !this.form.deptId) {
          resolve(false);
          return;
        }

        checkTeamName(this.form.teamName, this.form.deptId, this.form.teamId).then(response => {
          resolve(response.data);
        }).catch(error => {
          console.error('检查队伍名称重复失败:', error);
          this.$modal.msgError("检查队伍名称失败，请重试！");
          reject(error);
        });
      });
    },
  }
};
</script>

<style scoped>
.head-container {
  padding-bottom: 10px;
}

.link-type:hover {
  color: #66b3ff;
  text-decoration: underline;
}

/* 美化卡片头部 */
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card {
  margin-bottom: 20px;
}

.box-card .el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #EBEEF5;
  box-sizing: border-box;
}

.box-card .el-card__header span {
  font-weight: 500;
  color: #303133;
}

/* 美化空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: #fafbfc;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  margin: 10px 0;
}

/* 美化表格 */
.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table .el-table__row:hover > td {
  background-color: #f5f7fa;
}


.el-dialog__headerbtn .el-dialog__close {
  color: white;
}

.el-dialog__headerbtn .el-dialog__close:hover {
  color: #f0f0f0;
}

/* 美化树形控件 */
.el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #409EFF;
  color: white;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .el-col {
    margin-bottom: 20px;
  }

  .box-card {
    margin-bottom: 15px;
  }
}
</style>
