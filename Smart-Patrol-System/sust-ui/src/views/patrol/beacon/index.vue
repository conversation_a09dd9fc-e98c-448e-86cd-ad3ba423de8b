<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <!-- 第一行：逻辑编号、信标mac、UUID -->
      <el-row :gutter="10">
          <el-form-item label="信标编号" prop="beaconName">
            <el-input
              v-model="queryParams.beaconName"
              placeholder="请输入信标编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="信标mac" prop="beaconMac">
            <el-input
              v-model="queryParams.beaconMac"
              placeholder="请输入信标mac"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
<!--        <el-col :span="8" :xs="24">-->
<!--          <el-form-item label="UUID" prop="beaconUuid">-->
<!--            <el-input-->
<!--              v-model="queryParams.beaconUuid"-->
<!--              placeholder="请输入UUID"-->
<!--              clearable-->
<!--              @keyup.enter.native="handleQuery"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
      </el-row>
      <!-- 第二行：Major UUID、Minor UUID、按钮 -->
      <el-row :gutter="10" style="margin-top: 10px;">
        <!-- <el-col :span="8" :xs="24">
          <el-form-item label="Major UUID" prop="beaconMajor">
            <el-input
              v-model="queryParams.beaconMajor"
              placeholder="请输入Major UUID"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="Minor UUID" prop="beaconMinor">
            <el-input
              v-model="queryParams.beaconMinor"
              placeholder="请输入Minor UUID"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col> -->
        <!-- style="text-align: right;"按钮靠右，现在正常 -->
        <!-- <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['beacon:beacon:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['beacon:beacon:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['beacon:beacon:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['beacon:beacon:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['beacon:beacon:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="beaconList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="信标ID" align="center" prop="beaconId" /> -->
      <el-table-column label="信标编号" align="center" prop="beaconName" />
      <el-table-column label="信标mac" align="center" prop="beaconMac" />
      <el-table-column label="UUID" align="center" prop="beaconUuid" />
      <el-table-column label="Major UUID" align="center" prop="beaconMajor" />
      <el-table-column label="Minor UUID" align="center" prop="beaconMinor" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['beacon:beacon:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['beacon:beacon:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改信标管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="信标编号" prop="beaconName">
          <el-input v-model="form.beaconName" placeholder="请输入信标编号" />
        </el-form-item>
        <el-form-item label="信标mac" prop="beaconMac">
          <el-input v-model="form.beaconMac" placeholder="请输入信标mac" />
        </el-form-item>
        <el-form-item label="UUID" prop="beaconUuid">
          <el-input v-model="form.beaconUuid" placeholder="请输入UUID" />
        </el-form-item>
        <el-form-item label="Major UUID" prop="beaconMajor">
          <el-input v-model="form.beaconMajor" placeholder="请输入Major UUID" />
        </el-form-item>
        <el-form-item label="Minor UUID" prop="beaconMinor">
          <el-input v-model="form.beaconMinor" placeholder="请输入Minor UUID" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
<!--        <el-form-item label="逻辑删除" prop="delFlag">-->
<!--          <el-input v-model="form.delFlag" placeholder="请输入逻辑删除" />-->
<!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 信标导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的信标数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBeacon, getBeacon, delBeacon, addBeacon, updateBeacon  } from "@/api/beacon/beacon";
import { getToken } from "@/utils/auth";

export default {
  name: "Beacon",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 信标管理表格数据
      beaconList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        beaconName: null,
        beaconMac: null,
        beaconUuid: null,
        beaconMajor: null,
        beaconMinor: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "beacon/beacon/importData"
      },
      // 表单校验
      rules: {
        beaconName: [
          { required: true, message: "信标编号不能为空", trigger: "blur" }
        ],
        beaconMac: [
          { required: true, message: "信标mac不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
        delFlag: [
          { required: true, message: "逻辑删除不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询信标管理列表 */
    getList() {
      this.loading = true;
      listBeacon(this.queryParams).then(response => {
        this.beaconList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        beaconId: null,
        beaconName: null,
        beaconMac: null,
        beaconUuid: null,
        beaconMajor: null,
        beaconMinor: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        status: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.beaconId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加信标管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const beaconId = row.beaconId || this.ids
      getBeacon(beaconId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改信标管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.beaconId != null) {
            updateBeacon(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBeacon(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const beaconIds = row.beaconId || this.ids;
      this.$modal.confirm('是否确认删除信标管理编号为"' + beaconIds + '"的数据项？').then(function() {
        return delBeacon(beaconIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('beacon/beacon/export', {
        ...this.queryParams
      }, `beacon_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "信标导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('beacon/beacon/importTemplate', {
      }, `beacon_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }

};
</script>
