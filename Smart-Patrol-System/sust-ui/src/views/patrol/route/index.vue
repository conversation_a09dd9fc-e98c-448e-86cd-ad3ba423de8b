<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes :horizontal="this.$store.getters.device === 'mobile'" class="default-theme">
        <!--部门数据-->
        <pane size="20">
          <el-col>
            <div class="head-container">
              <h4 style="margin: 0 0 15px 0; color: #606266; font-size: 14px;">
                <i class="el-icon-office-building"></i> 部门列表
              </h4>
              <el-input
                v-model="deptName"
                placeholder="请输入部门名称"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 15px" />
              <el-tree
                :data="deptOptions"
                :props="defaultProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="deptTree"
                node-key="id"
                default-expand-all
                highlight-current
                @node-click="handleNodeClick" />
            </div>
          </el-col>
        </pane>
        <!--路线数据-->
        <pane size="80">
          <el-col>
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
              <el-form-item label="路线名称" prop="routeName">
                <el-input
                  v-model="queryParams.routeName"
                  placeholder="请输入路线名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <!--      <el-form-item label="所属部门" prop="detpId">-->
              <!--        <el-input-->
              <!--          v-model="queryParams.detpId"-->
              <!--          placeholder="请输入所属部门"-->
              <!--          clearable-->
              <!--          @keyup.enter.native="handleQuery"-->
              <!--        />-->
              <!--      </el-form-item>-->

              <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                  <el-option
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAdd"
                  v-hasPermi="['patrol:route:add']"
                >新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdate"
                  v-hasPermi="['patrol:route:edit']"
                >修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['patrol:route:remove']"
                >删除</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="warning"
                  plain
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExport"
                  v-hasPermi="['patrol:route:export']"
                >导出</el-button>
              </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="routeList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" align="center" />
              <!--      <el-table-column label="路线ID" align="center" prop="routeId" />-->
              <el-table-column label="路线名称" align="center" prop="routeName" show-overflow-tooltip />
              <el-table-column label="所属部门" align="center" prop="deptName" />
              <el-table-column label="关联点位" align="center" width="100">
                <template slot-scope="scope">
                  <el-tag type="info" size="small">
                    {{ scope.row.patrolPoints ? scope.row.patrolPoints.length : 0 }}个
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-view"
                    @click="handleViewPoints(scope.row)"
                    v-hasPermi="['patrol:route:query']"
                  >查看点位</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:route:edit']"
                  >修改</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['patrol:route:remove']"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />

            <!-- 查看点位对话框 -->
            <el-dialog title="查看路线点位" :visible.sync="pointsDialogVisible" width="1200px" append-to-body>
              <div v-if="currentRouteInfo">
                <!-- 基本信息 -->
                <el-descriptions :column="4" border>
                  <el-descriptions-item label="路线名称">{{ currentRouteInfo.routeName }}</el-descriptions-item>
                  <el-descriptions-item label="所属部门">{{ currentRouteInfo.deptName }}</el-descriptions-item>
                  <el-descriptions-item label="路线状态">
                    <dict-tag :options="dict.type.sys_normal_disable" :value="currentRouteInfo.status"/>
                  </el-descriptions-item>
                  <el-descriptions-item label="点位数量">{{ currentRouteInfo.patrolPoints ? currentRouteInfo.patrolPoints.length : 0 }}</el-descriptions-item>
                </el-descriptions>

                <el-divider content-position="left">点位顺序展示</el-divider>

                <!-- 点位管理区域（只读） -->
                <div class="point-management view-only">
                  <div class="management-header">
                    <div class="header-info">
                      <i class="el-icon-info" style="color: #909399; margin-right: 10px;"></i>
                      <span style="color: #606266; font-size: 14px;">
                当前路线共有 {{ currentRouteInfo.patrolPoints ? currentRouteInfo.patrolPoints.length : 0 }} 个点位
              </span>
                    </div>
                  </div>

                  <!-- 横向路线轴（只读） -->
                  <div v-if="currentRouteInfo.patrolPoints && currentRouteInfo.patrolPoints.length > 0" class="horizontal-timeline">
                    <div class="timeline-points view-mode">
                      <div
                        v-for="(point, index) in currentRouteInfo.patrolPoints"
                        :key="point.pointId"
                        class="timeline-point"
                        :class="{'selected': viewSelectedPointIndex === index}"
                        @click="selectViewPoint(index)">
                        <div class="point-circle" :class="{'disabled': point.status === '1', 'selected': viewSelectedPointIndex === index}">
                          <span class="point-number">{{ index + 1 }}</span>
                        </div>
                        <div class="point-label">{{ point.pointName }}</div>
                      </div>
                    </div>
                  </div>

                  <el-empty v-else description="该路线暂未关联任何点位" :image-size="60" />

                  <!-- 点位详情显示区域（只读） -->
                  <div v-if="viewSelectedPointIndex >= 0 && currentRouteInfo.patrolPoints[viewSelectedPointIndex]" class="point-detail-panel" v-loading="viewPointDetailLoading">
                    <el-divider content-position="left">
                      <i class="el-icon-location-information"></i>
                      点位详情 - {{ currentRouteInfo.patrolPoints[viewSelectedPointIndex].pointName }}
                    </el-divider>

                    <el-descriptions :column="2" border size="small" v-if="viewPointDetail">
                      <el-descriptions-item label="点位名称">{{ viewPointDetail.pointName }}</el-descriptions-item>
                      <el-descriptions-item label="所属区域">{{ findAreaNameById(areaOptions, viewPointDetail.areaId) }}</el-descriptions-item>
                      <el-descriptions-item label="经度">{{ viewPointDetail.longtitude }}</el-descriptions-item>
                      <el-descriptions-item label="纬度">{{ viewPointDetail.latitude }}</el-descriptions-item>
                      <el-descriptions-item label="巡检顺序">第{{ viewSelectedPointIndex + 1 }}个</el-descriptions-item>
                      <el-descriptions-item label="点位状态">
                        <dict-tag :options="dict.type.sys_normal_disable" :value="currentRouteInfo.patrolPoints[viewSelectedPointIndex].status"/>
                      </el-descriptions-item>
                    </el-descriptions>

                    <div style="margin-top: 15px;" v-if="viewPointDetail">
                      <h5 style="margin: 0 0 10px 0; color: #606266;">
                        <i class="el-icon-menu"></i>
                        检查项列表 ({{ viewPointDetail.patrolPointItemList ? viewPointDetail.patrolPointItemList.length : 0 }}项)
                      </h5>

                      <el-table
                        :data="viewPointDetail.patrolPointItemList"
                        style="width: 100%"
                        size="small"
                        v-if="viewPointDetail.patrolPointItemList && viewPointDetail.patrolPointItemList.length > 0">
                        <el-table-column prop="itemName" label="检查项名称" align="center" />
                        <el-table-column prop="description" label="描述" align="center" show-overflow-tooltip />
                        <el-table-column prop="status" label="状态" width="100" align="center">
                          <template slot-scope="scope">
                            <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                          </template>
                        </el-table-column>
                      </el-table>

                      <el-empty v-else description="该点位暂无检查项" :image-size="40" />
                    </div>
                  </div>
                </div>
              </div>
              <div slot="footer" class="dialog-footer">
                <el-button @click="pointsDialogVisible = false">关 闭</el-button>
              </div>
            </el-dialog>

            <!-- 添加或修改巡检路线对话框 -->
            <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
              <!-- 基本信息表单 -->
              <el-form ref="form" :model="form" :rules="rules" label-width="80px" :inline="true">
                <el-form-item label="路线名称" prop="routeName">
                  <el-input v-model="form.routeName" placeholder="请输入路线名称" style="width: 200px;" />
                </el-form-item>
                <el-form-item label="所属部门" prop="deptId">
                  <treeselect v-model="form.deptId" style="width: 200px;" :options="enabledDeptOptions" :show-count="true" placeholder="请选择所属部门" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                  <el-select v-model="form.status" placeholder="请选择状态" style="width: 120px;">
                    <el-option
                      v-for="dict in dict.type.sys_normal_disable"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-form>

              <el-divider content-position="left">点位顺序管理</el-divider>


              <!-- 点位管理区域 -->
              <div class="point-management">
                <div class="management-header">
                  <div class="header-info">
                    <el-tooltip content="拖拽点位可调整顺序" placement="top">
                      <i class="el-icon-info" style="color: #909399; margin-right: 10px;"></i>
                    </el-tooltip>
                    <span style="color: #606266; font-size: 14px;">
              当前已关联 {{ currentRoutePoints.length }} 个点位
              <el-tag v-if="currentRoutePoints.length > 10" type="warning" size="mini" style="margin-left: 8px;">
                点位较多，建议分组管理
              </el-tag>
            </span>
                  </div>
                  <div class="header-actions">
                    <el-button-group v-if="currentRoutePoints.length > 5" size="mini" style="margin-right: 10px;">
                      <el-button icon="el-icon-arrow-left" @click="scrollToFirst">首个</el-button>
                      <el-button icon="el-icon-arrow-right" @click="scrollToLast">末个</el-button>
                    </el-button-group>
                    <el-button size="mini" type="primary" icon="el-icon-plus" @click="addPoint">添加点位</el-button>
                  </div>
                </div>

                <!-- 横向路线轴 -->
                <div v-if="currentRoutePoints && currentRoutePoints.length > 0" class="horizontal-timeline">
                  <draggable
                    v-model="currentRoutePoints"
                    @change="handlePointOrderChange"
                    :options="{animation: 300, ghostClass: 'ghost-point'}"
                    class="timeline-points" :class="getTimelineClass()">
                    <div
                      v-for="(point, index) in currentRoutePoints"
                      :key="point.pointId"
                      class="timeline-point"
                      :class="{'selected': selectedPointIndex === index}"
                      @click="selectPoint(index)"
                      :ref="`point-${index}`">
                      <div class="point-circle" :class="{'disabled': point.status === '1', 'selected': selectedPointIndex === index}">
                        <span class="point-number">{{ index + 1 }}</span>
                      </div>
                      <div class="point-label">{{ point.pointName }}</div>
                      <div class="point-actions" @click.stop>
                        <el-tooltip content="启用/禁用" placement="top">
                          <el-switch
                            v-model="point.status"
                            active-value="0"
                            inactive-value="1"
                            size="mini"
                            @change="handlePointStatusChange(point, $event)"
                            @click.stop>
                          </el-switch>
                        </el-tooltip>
                        <el-tooltip content="移除点位" placement="top">
                          <el-button
                            type="text"
                            size="mini"
                            icon="el-icon-delete"
                            @click.stop="removePoint(index)"
                            style="color: #f56c6c; margin-left: 5px;">
                          </el-button>
                        </el-tooltip>
                      </div>

                    </div>
                  </draggable>
                </div>

                <el-empty v-else description="该路线暂未关联任何点位" :image-size="60" />

                <!-- 点位详情显示区域 -->
                <div v-if="selectedPointIndex >= 0 && currentRoutePoints[selectedPointIndex]" class="point-detail-panel">
                  <el-divider content-position="left">
                    <i class="el-icon-location-information"></i>
                    点位详情 - {{ currentRoutePoints[selectedPointIndex].pointName }}
                  </el-divider>

                  <el-descriptions :column="2" border size="small">
                    <el-descriptions-item label="点位名称">{{ currentRoutePoints[selectedPointIndex].pointName }}</el-descriptions-item>
                    <el-descriptions-item label="巡检顺序">第{{ selectedPointIndex + 1 }}个</el-descriptions-item>
                    <el-descriptions-item label="点位状态">
                      <dict-tag :options="dict.type.sys_normal_disable" :value="currentRoutePoints[selectedPointIndex].status"/>
                    </el-descriptions-item>
                    <el-descriptions-item label="所属区域" v-if="editPointDetail">
                      {{ findAreaNameById(areaOptions, editPointDetail.areaId) }}
                    </el-descriptions-item>
                  </el-descriptions>

                  <div style="margin-top: 15px;" v-if="editPointDetail">
                    <h5 style="margin: 0 0 10px 0; color: #606266;">
                      <i class="el-icon-menu"></i>
                      检查项列表 ({{ editPointDetail.patrolPointItemList ? editPointDetail.patrolPointItemList.length : 0 }}项)
                    </h5>

                    <el-table
                      :data="editPointDetail.patrolPointItemList"
                      style="width: 100%"
                      size="small"
                      v-if="editPointDetail.patrolPointItemList && editPointDetail.patrolPointItemList.length > 0">
                      <el-table-column prop="itemName" label="检查项名称" align="center" />
                      <el-table-column prop="description" label="描述" align="center" show-overflow-tooltip />
                      <el-table-column prop="status" label="状态" width="100" align="center">
                        <template slot-scope="scope">
                          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                        </template>
                      </el-table-column>
                    </el-table>

                    <el-empty v-else description="该点位暂无检查项" :image-size="40" />
                  </div>
                </div>
              </div>
              <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
              </div>
            </el-dialog>



            <!-- 点位选择对话框 -->
            <el-dialog title="选择点位" :visible.sync="pointSelectDialogVisible" width="1000px" append-to-body>
              <!-- 搜索表单 -->
              <el-form :model="pointSelectQueryParams" ref="pointSelectQueryForm" size="small" :inline="true" label-width="68px">
                <el-form-item label="点位名称" prop="pointName">
                  <el-input
                    v-model="pointSelectQueryParams.pointName"
                    placeholder="请输入点位名称"
                    clearable
                    @keyup.enter.native="handlePointSelectQuery"
                    style="width: 200px;"
                  />
                </el-form-item>
                <el-form-item label="区域" prop="areaId">
                  <treeselect
                    v-model="pointSelectQueryParams.areaId"
                    :options="areaOptions"
                    :normalizer="normalizer"
                    placeholder="请选择区域"
                    style="width: 200px;"
                  />
                </el-form-item>
                <!-- <el-form-item label="信标ID" prop="beaconId">
                  <el-input
                    v-model="pointSelectQueryParams.beaconId"
                    placeholder="请输入信标ID"
                    clearable
                    @keyup.enter.native="handlePointSelectQuery"
                    style="width: 150px;"
                  />
                </el-form-item> -->
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handlePointSelectQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetPointSelectQuery">重置</el-button>
                </el-form-item>
              </el-form>

              <!-- 点位列表表格 -->
              <el-table
                v-loading="pointSelectLoading"
                :data="availablePointList"
                @selection-change="handlePointSelectionChange"
                style="margin-top: 15px;">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="点位名称" align="center" prop="pointName" show-overflow-tooltip />
                <el-table-column label="所属区域" align="center" prop="areaId" :formatter="(row, column) => findAreaNameById(areaOptions,row.areaId)"/>
<!--                <el-table-column label="区域名称" align="center" prop="areaName" show-overflow-tooltip />-->
                <!--                <el-table-column label="信标ID" align="center" prop="beaconId" />-->
                <el-table-column label="经度" align="center" prop="longtitude" width="100" />
                <el-table-column label="纬度" align="center" prop="latitude" width="100" />
                <el-table-column label="状态" align="center" prop="status" width="80">
                  <template slot-scope="scope">
                    <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="检查项" align="center" width="80">
                  <template slot-scope="scope">
                    <el-tag type="info" size="small">
                      {{ scope.row.patrolPointItemList ? scope.row.patrolPointItemList.length : 0 }}项
                    </el-tag>
                  </template>
                </el-table-column> -->
                <el-table-column label="路线状态" align="center" width="100">
                  <template slot-scope="scope">
                    <el-tag
                      v-if="isPointInCurrentRoute(scope.row.pointId)"
                      type="warning"
                      size="small">
                      已添加
                    </el-tag>
                    <el-tag
                      v-else
                      type="success"
                      size="small">
                      可添加
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页组件 -->
              <pagination
                v-show="pointSelectTotal > 0"
                :total="pointSelectTotal"
                :page.sync="pointSelectQueryParams.pageNum"
                :limit.sync="pointSelectQueryParams.pageSize"
                @pagination="getPointSelectList"
                style="margin-top: 15px;"
              />

              <!-- 选择统计信息 -->
              <div class="selection-info" v-if="selectedPointIds.length > 0">
                <el-alert
                  :title="`已选择 ${selectedPointIds.length} 个点位`"
                  type="info"
                  :closable="false"
                  show-icon
                  style="margin-top: 15px;">
                </el-alert>
              </div>

              <div slot="footer" class="dialog-footer">
                <el-button @click="pointSelectDialogVisible = false">取 消</el-button>
                <el-button
                  type="primary"
                  @click="confirmAddPoints"
                  :disabled="selectedPointIds.length === 0">
                  确定添加 ({{ selectedPointIds.length }})
                </el-button>
              </div>
            </el-dialog>
          </el-col>
        </pane>
      </splitpanes>
    </el-row>
  </div>
</template>

<script>
import { listRoute, getRoute, delRoute, addRoute, updateRoute } from "@/api/patrol/route";
import { listPoint, pointSelect, getPoint } from "@/api/patrol/point";
import { areaTreeSelect } from "@/api/patrol/area";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { deptTreeSelect } from "@/api/system/user";
import draggable from 'vuedraggable';
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

export default {
  name: "Route",
  dicts: ['sys_normal_disable'],
  components: { Treeselect, draggable, Splitpanes, Pane },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 巡检路线表格数据
      routeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        routeName: null,
        deptId: null,
        status: null,
      },
      // 表单参数
      form: {},
      enabledDeptOptions: undefined,
      // 查看点位对话框显示状态
      pointsDialogVisible: false,
      // 当前查看的路线信息
      currentRouteInfo: null,
      // 当前路线的点位信息（用于修改对话框显示）
      currentRoutePoints: [],
      // 查看模式的点位详情
      viewPointDetail: null,
      viewPointDetailLoading: false,
      // 编辑模式的点位详情
      editPointDetail: null,
      // 当前选中的点位索引
      selectedPointIndex: -1,
      // 查看模式选中的点位索引
      viewSelectedPointIndex: -1,
      // 部门数据
      deptOptions: undefined,
      // 部门名称（用于搜索）
      deptName: undefined,
      // 树形组件属性
      defaultProps: {
        children: "children",
        label: "label"
      },

      // ========== 点位选择对话框相关数据 ==========
      // 点位选择对话框显示状态
      pointSelectDialogVisible: false,
      // 点位选择对话框加载状态
      pointSelectLoading: false,
      // 可选点位列表
      availablePointList: [],
      // 点位选择总数
      pointSelectTotal: 0,
      // 点位选择查询参数
      pointSelectQueryParams: {
        pageNum: 1,
        pageSize: 10,
        pointName: null,
        areaId: null,
        beaconId: null,
        status: '0', // 默认只查询启用状态的点位
      },
      // 选中的点位IDs
      selectedPointIds: [],
      // 区域选项
      areaOptions: [],
      // 区域树形数据转换器
      normalizer(node) {
        return {
          id: node.id,
          label: node.label,
          children: node.children
        }
      },

      // 表单校验
      rules: {
        routeName: [
          { required: true, message: "路线名称不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "所属部门不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "逻辑删除不能为空", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.deptTree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
    this.getAreaTree();
  },
  methods: {
    /** 查询巡检路线列表 */
    getList() {
      this.loading = true;
      listRoute(this.queryParams).then(response => {
        this.routeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
        this.enabledDeptOptions = this.filterDisabledDept(JSON.parse(JSON.stringify(response.data)));
      });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter(dept => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        routeId: null,
        routeName: null,
        deptId: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.currentRoutePoints = [];
      this.selectedPointIndex = -1;
      this.editPointDetail = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.deptTree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.routeId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 选择查看模式的点位 */
    selectViewPoint(index) {
      this.viewSelectedPointIndex = index;
      this.viewPointDetailLoading = true;
      this.viewPointDetail = null;

      // 获取点位详细信息
      const currentPoint = this.currentRouteInfo.patrolPoints[index];
      if (currentPoint && currentPoint.pointId) {
        getPoint(currentPoint.pointId).then(response => {
          this.viewPointDetail = response.data;
          this.viewPointDetailLoading = false;
        }).catch(error => {
          console.error('获取点位详情失败:', error);
          this.viewPointDetailLoading = false;
        });
      }
    },

    /** 根据区域ID查找区域名称 */
    findAreaNameById(treeData, id) {
      if(!treeData || !id) return '';
      for (const node of treeData) {
        if (node.id === id) {
          return node.label; // 找到匹配项，返回区域名称
        }
        if (node.children && node.children.length) {
          const found = this.findAreaNameById(node.children, id); // 递归查找子节点
          if (found) return found; // 如果在子节点中找到，返回结果
        }
      }
      return ''; // 未找到
    },
    /** 查看点位按钮操作 */
    handleViewPoints(row) {
      const routeId = row.routeId;
      getRoute(routeId).then(response => {
        this.currentRouteInfo = response.data;
        this.viewSelectedPointIndex = -1; // 重置选中状态
        this.viewPointDetail = null; // 重置点位详情
        this.pointsDialogVisible = true;
      }).catch(() => {
        this.$modal.msgError("获取路线详情失败");
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加巡检路线";

    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const routeId = row.routeId || this.ids
      getRoute(routeId).then(response => {
        this.form = response.data;

        // 设置当前路线的点位信息用于显示
        if (response.data.patrolPoints && response.data.patrolPoints.length > 0) {
          this.currentRoutePoints = response.data.patrolPoints.sort((a, b) => a.pointOrder - b.pointOrder);
        } else {
          this.currentRoutePoints = [];
        }

        this.open = true;
        this.title = "修改巡检路线";
      }).catch(error => {
        console.error('获取路线详情失败:', error);
        this.$modal.msgError("获取路线详情失败");
      });
    },
    /** 处理点位顺序变化 */
    handlePointOrderChange() {
      // 更新点位顺序
      this.currentRoutePoints.forEach((point, index) => {
        point.pointOrder = index;
      });
      console.log('点位顺序已更新:', this.currentRoutePoints);
    },

    /** 移除点位 */
    removePoint(index) {
      this.$confirm('确定要移除该点位吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.currentRoutePoints.splice(index, 1);
        // 重新排序
        this.handlePointOrderChange();
        this.$message.success('点位已移除');
      }).catch(() => {});
    },
    /** 选择点位 */
    selectPoint(index) {
      this.selectedPointIndex = index;
      this.editPointDetail = null;

      // 获取点位详细信息
      const currentPoint = this.currentRoutePoints[index];
      if (currentPoint && currentPoint.pointId) {
        getPoint(currentPoint.pointId).then(response => {
          this.editPointDetail = response.data;
        }).catch(error => {
          console.error('获取点位详情失败:', error);
        });
      }

      // 自动滚动到选中的点位
      this.scrollToPoint(index);
    },
    /** 获取时间轴样式类 */
    getTimelineClass() {
      const count = this.currentRoutePoints.length;
      if (count > 15) return 'very-many-points';
      if (count > 8) return 'many-points';
      return '';
    },
    /** 滚动到指定点位 */
    scrollToPoint(index) {
      this.$nextTick(() => {
        const pointRef = this.$refs[`point-${index}`];
        if (pointRef && pointRef[0]) {
          pointRef[0].scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
          });
        }
      });
    },
    /** 滚动到第一个点位 */
    scrollToFirst() {
      this.scrollToPoint(0);
    },
    /** 滚动到最后一个点位 */
    scrollToLast() {
      this.scrollToPoint(this.currentRoutePoints.length - 1);
    },
    /** 添加点位 */
    addPoint() {
      // 重置点位选择相关数据
      this.selectedPointIds = [];
      this.pointSelectQueryParams = {
        pageNum: 1,
        pageSize: 10,
        pointName: null,
        areaId: null,
        beaconId: null,
        status: '0', // 只查询启用状态的点位
      };

      // 获取点位列表
      this.getPointSelectList();

      // 显示点位选择对话框
      this.pointSelectDialogVisible = true;
    },

    // ========== 点位选择相关方法 ==========
    /** 获取区域树结构 */
    getAreaTree() {
      areaTreeSelect().then(response => {
        this.areaOptions = response.data;
      });
    },

    /** 获取可选点位列表 */
    getPointSelectList() {
      this.pointSelectLoading = true;
      listPoint(this.pointSelectQueryParams).then(response => {
        // 暂时不在前端过滤，显示所有点位
        // 这样可以保持分页的正确性
        this.availablePointList = response.rows;
        this.pointSelectTotal = response.total;
        this.pointSelectLoading = false;
      }).catch(() => {
        this.pointSelectLoading = false;
      });
    },

    /** 点位选择查询 */
    handlePointSelectQuery() {
      this.pointSelectQueryParams.pageNum = 1;
      this.getPointSelectList();
    },

    /** 重置点位选择查询 */
    resetPointSelectQuery() {
      this.pointSelectQueryParams = {
        pageNum: 1,
        pageSize: 10,
        pointName: null,
        areaId: null,
        beaconId: null,
        status: '0',
      };
      this.getPointSelectList();
    },

    /** 处理点位选择变化 */
    handlePointSelectionChange(selection) {
      this.selectedPointIds = selection.map(item => item.pointId);
    },

    /** 确认添加选中的点位 */
    confirmAddPoints() {
      if (this.selectedPointIds.length === 0) {
        this.$message.warning('请先选择要添加的点位');
        return;
      }

      // 获取选中的点位详细信息
      const selectedPoints = this.availablePointList.filter(point =>
        this.selectedPointIds.includes(point.pointId)
      );

      // 检查重复点位
      const currentPointIds = this.currentRoutePoints.map(p => p.pointId);
      const duplicatePoints = selectedPoints.filter(point =>
        currentPointIds.includes(point.pointId)
      );
      const validPoints = selectedPoints.filter(point =>
        !currentPointIds.includes(point.pointId)
      );

      // // 如果有重复点位，给出提示
      // if (duplicatePoints.length > 0) {
      //   const duplicateNames = duplicatePoints.map(p => p.pointName).join('、');
      //   this.$message.warning(`点位 "${duplicateNames}" 已存在于当前路线中，将跳过添加`);
      // }

      // 如果没有有效点位可添加
      if (validPoints.length === 0) {
        this.$message.warning('所选点位均已存在于当前路线中');
        return;
      }

      // 添加有效的点位到当前路线点位列表中
      validPoints.forEach(point => {
        const newPoint = {
          pointId: point.pointId,
          pointName: point.pointName,
          pointOrder: this.currentRoutePoints.length,
          status: '0', // 默认启用状态
        };
        this.currentRoutePoints.push(newPoint);
      });

      // 重新排序
      this.handlePointOrderChange();

      // 关闭对话框
      this.pointSelectDialogVisible = false;

      // 提示成功
      if (duplicatePoints.length > 0) {
        this.$message.success(`成功添加 ${validPoints.length} 个点位，跳过 ${duplicatePoints.length} 个重复点位`);
      } else {
        this.$message.success(`成功添加 ${validPoints.length} 个点位`);
      }

      // 如果添加的点位较多，自动滚动到最后一个
      if (validPoints.length > 0) {
        this.$nextTick(() => {
          this.scrollToLast();
        });
      }
    },
    /** 处理点位状态变化 */
    handlePointStatusChange(point, newStatus) {
      // 阻止事件冒泡，避免触发点位详情弹窗
      console.log(`点位 ${point.pointName} 状态已更改为: ${newStatus === '0' ? '启用' : '禁用'}`);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 构建提交数据，包含点位顺序和状态信息
          const submitData = {
            routeId: this.form.routeId,
            routeName: this.form.routeName,
            deptId: this.form.deptId,
            status: this.form.status,
            // 提交点位详细信息
            routePoints: this.currentRoutePoints.map(point => ({
              pointId: point.pointId,
              pointOrder: point.pointOrder,
              status: point.status
            }))
          };

          if (this.form.routeId != null) {
            updateRoute(submitData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRoute(submitData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const routeIds = row.routeId || this.ids;
      this.$modal.confirm('是否确认删除巡检路线编号为"' + routeIds + '"的数据项？').then(function() {
        return delRoute(routeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('patrol/route/export', {
        ...this.queryParams
      }, `route_${new Date().getTime()}.xlsx`)
    },

    /** 检查点位是否已在当前路线中 */
    isPointInCurrentRoute(pointId) {
      return this.currentRoutePoints.some(p => p.pointId === pointId);
    }

  }
};
</script>

<style scoped>
.el-descriptions {
  margin-bottom: 20px;
}

.el-divider {
  margin: 20px 0;
}

.el-empty {
  padding: 40px 0;
}

.el-tag {
  margin: 2px;
}

.route-points-display {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.route-points-display .el-table {
  background-color: transparent;
}

.expand-content {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 5px 0;
}

.expand-content h4 {
  color: #606266;
  font-weight: 500;
}

/* 点位管理样式 */
.point-management {
  min-height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
}

/* 管理头部 */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.header-info {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 横向时间轴容器 */
.horizontal-timeline {
  position: relative;
  flex: 1;
  padding: 60px 20px 40px;
  overflow-x: auto;
  overflow-y: hidden;
  min-height: 200px;
  /* 优化滚动体验 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 滚动条样式优化 */
.horizontal-timeline::-webkit-scrollbar {
  height: 8px;
}

.horizontal-timeline::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.horizontal-timeline::-webkit-scrollbar-thumb {
  background: #409eff;
  border-radius: 4px;
}

.horizontal-timeline::-webkit-scrollbar-thumb:hover {
  background: #66b1ff;
}

/* 时间轴点位容器 */
.timeline-points {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 100px;
  padding: 0 50px;
  min-width: fit-content;
  position: relative;
}

/* 响应式设计 - 点位较多时自动调整 */
.timeline-points.many-points {
  gap: 80px;
}

.timeline-points.very-many-points {
  gap: 60px;
}

/* 当点位超过10个时，使用更紧凑的布局 */
@media (max-width: 1400px) {
  .timeline-points {
    gap: 80px;
  }

  .timeline-points.many-points {
    gap: 60px;
  }

  .timeline-points.very-many-points {
    gap: 50px;
  }
}

/* 点位连接箭头 - 更直观的顺序指示 */
.timeline-point:not(:last-child)::after {
  content: '→';
  position: absolute;
  top: 15px;
  right: -60px;
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* 时间轴点位 */
.timeline-point {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  z-index: 2;
}

.timeline-point:hover {
  transform: translateY(-5px);
}

.timeline-point.selected {
  transform: translateY(-8px);
}

/* 点位圆圈 */
.point-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 3;
  border: 3px solid white;
}

.point-circle.disabled {
  background: linear-gradient(135deg, #c0c4cc, #909399);
  box-shadow: 0 4px 12px rgba(192, 196, 204, 0.3);
}

.point-circle.selected {
  background: linear-gradient(135deg, #f56c6c, #e6a23c);
  box-shadow: 0 6px 20px rgba(245, 108, 108, 0.5);
  border-color: #f56c6c;
  transform: scale(1.15);
}

.point-circle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.point-circle.selected:hover {
  transform: scale(1.15);
  box-shadow: 0 8px 24px rgba(245, 108, 108, 0.6);
}

.point-number {
  font-size: 14px;
}

/* 点位标签 */
.point-label {
  margin-top: 15px;
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  text-align: center;
  max-width: 80px;
  word-break: break-all;
  line-height: 1.2;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

/* 紧凑模式下的标签样式 */
.timeline-points.many-points .point-label {
  font-size: 11px;
  max-width: 70px;
  padding: 3px 6px;
}

.timeline-points.very-many-points .point-label {
  font-size: 10px;
  max-width: 60px;
  padding: 2px 4px;
}

/* 点位操作按钮 */
.point-actions {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
  background: white;
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 4;
}

.timeline-point:hover .point-actions {
  opacity: 1;
}

/* 拖拽时的幽灵样式 */
.ghost-point {
  opacity: 0.5;
  transform: scale(0.9) rotate(5deg);
}

/* 点位详情面板 */
.point-detail-panel {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.point-detail-panel .el-descriptions {
  margin-bottom: 15px;
}

.point-detail-panel h5 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

/* 查看模式样式 */
.point-management.view-only {
  background-color: #f8f9fa;
}

.timeline-points.view-mode .timeline-point {
  cursor: pointer;
}

.timeline-points.view-mode .point-actions {
  display: none;
}

/* 部门树样式 */
.head-container {
  padding: 10px;
}

.head-container .el-input {
  margin-bottom: 15px;
}

.head-container .el-tree {
  background: transparent;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  max-height: 600px;
  overflow-y: auto;
}

/* splitpanes样式调整 */
.splitpanes.default-theme .splitpanes__pane {
  background: #fff;
}

.splitpanes.default-theme .splitpanes__splitter {
  background-color: #f0f2f5;
  border-left: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
}

.splitpanes.default-theme .splitpanes__splitter:hover {
  background-color: #409eff;
}

/* 点位选择对话框样式 */
.selection-info {
  margin-top: 15px;
}

.selection-info .el-alert {
  border-radius: 4px;
}

/* 点位选择表格样式优化 */
.point-select-table {
  margin-top: 15px;
}

.point-select-table .el-table__header-wrapper {
  background-color: #f8f9fa;
}

.point-select-table .el-table__header th {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

/* 搜索表单样式 */
.point-select-form {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 15px;
}

.point-select-form .el-form-item {
  margin-bottom: 10px;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .point-select-form .el-form-item {
    display: block;
    margin-right: 0;
  }

  .point-select-form .el-input,
  .point-select-form .vue-treeselect {
    width: 100% !important;
  }
}
</style>
