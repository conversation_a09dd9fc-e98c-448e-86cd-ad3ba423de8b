<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="巡检路线" prop="routeId">
        <el-select
          v-model="queryParams.routeId"
          placeholder="请选择巡检路线"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="route in routeOptions"
            :key="route.routeId"
            :label="route.routeName"
            :value="route.routeId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划名称" prop="scheduleName">
        <el-input
          v-model="queryParams.scheduleName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="巡逻队伍" prop="teamId">
        <el-select
          v-model="queryParams.teamId"
          placeholder="请选择巡逻队伍"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="team in teamOptions"
            :key="team.teamId"
            :label="team.teamName"
            :value="team.teamId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['schedule:schedule:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['schedule:schedule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['schedule:schedule:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['schedule:schedule:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scheduleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="100" align="center" />
<!--      <el-table-column label="计划ID" align="center" prop="scheduleId" />-->
      <el-table-column label="计划名称" align="center" prop="scheduleName" />
      <el-table-column label="路线名称" align="center" prop="routeName" />
      <el-table-column label="队伍名称" align="center" prop="teamName" />
      <el-table-column label="每日开始时间" align="center" prop="scheduledStartTime" width="220">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.scheduledStartTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时长（分钟）" align="center" prop="duration" />
      <el-table-column label="执行周期" align="center" prop="period">
        <template slot-scope="scope">
          <span>{{ formatPeriod(scope.row.period) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['schedule:schedule:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['schedule:schedule:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计划表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">

        <el-form-item label="计划名称" prop="scheduleName">
          <el-input v-model="form.scheduleName" placeholder="请输入计划名称" />
        </el-form-item>
        <el-form-item label="巡检路线" prop="routeId">
          <el-select v-model="form.routeId" placeholder="请选择巡检路线" clearable style="width: 100%">
            <el-option
              v-for="route in routeOptions"
              :key="route.routeId"
              :label="route.routeName"
              :value="route.routeId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="巡逻队伍" prop="teamId">
          <el-select v-model="form.teamId" placeholder="请选择巡逻队伍" clearable style="width: 100%">
            <el-option
              v-for="team in teamOptions"
              :key="team.teamId"
              :label="team.teamName"
              :value="team.teamId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="每日开始时间" prop="scheduledStartTime">
          <el-time-picker
            v-model="form.scheduledStartTime"
            value-format="HH:mm:ss"
            placeholder="请选择每日开始时间">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="时长（分钟）" prop="duration">
          <el-input-number v-model="form.duration" :min="1" :max="1440" placeholder="请输入时长" style="width: 100%" />
        </el-form-item>
        <el-form-item label="执行周期" prop="period">
          <el-checkbox-group v-model="periodArray">
            <el-checkbox :label="1">周一</el-checkbox>
            <el-checkbox :label="2">周二</el-checkbox>
            <el-checkbox :label="3">周三</el-checkbox>
            <el-checkbox :label="4">周四</el-checkbox>
            <el-checkbox :label="5">周五</el-checkbox>
            <el-checkbox :label="6">周六</el-checkbox>
            <el-checkbox :label="7">周日</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSchedule, getSchedule, delSchedule, addSchedule, updateSchedule } from "@/api/schedule/schedule";
import { listSimpleRoute } from "@/api/patrol/route";
import { listTeam } from "@/api/patrol/team";

export default {
  name: "Schedule",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划表表格数据
      scheduleList: [],
      // 路线选项
      routeOptions: [],
      // 队伍选项
      teamOptions: [],
      // 周期数组
      periodArray: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        routeId: null,
        scheduleName: null,
        teamId: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        routeId: [
          { required: true, message: "请选择巡检路线", trigger: "change" }
        ],
        scheduleName: [
          { required: true, message: "计划名称不能为空", trigger: "blur" }
        ],
        teamId: [
          { required: true, message: "请选择巡逻队伍", trigger: "change" }
        ],
        scheduledStartTime: [
          { required: true, message: "请选择每日开始时间", trigger: "change" }
        ],
        duration: [
          { required: true, message: "请输入时长", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getRouteOptions();
    this.getTeamOptions();
  },
  methods: {
    /** 查询计划表列表 */
    getList() {
      this.loading = true;
      listSchedule(this.queryParams).then(response => {
        this.scheduleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取路线选项 */
    getRouteOptions() {
      listSimpleRoute({ status: '0' }).then(response => {
        this.routeOptions = response.data || [];
      });
    },
    /** 获取队伍选项 */
    getTeamOptions() {
      listTeam({ status: '0' }).then(response => {
        this.teamOptions = response.rows || [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        scheduleId: null,
        routeId: null,
        scheduleName: null,
        teamId: null,
        scheduledStartTime: null,
        duration: null,
        period: null,
        status: '0',
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.periodArray = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.scheduleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加计划表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const scheduleId = row.scheduleId || this.ids
      getSchedule(scheduleId).then(response => {
        this.form = response.data;
        // 处理周期数据
        if (this.form.period) {
          this.periodArray = this.form.period.split(',').map(Number);
        }
        this.open = true;
        this.title = "修改计划表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理周期数据
          this.form.period = this.periodArray.join(',');

          if (this.form.scheduleId != null) {
            updateSchedule(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSchedule(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const scheduleIds = row.scheduleId || this.ids;
      this.$modal.confirm('是否确认删除计划表编号为"' + scheduleIds + '"的数据项？').then(function() {
        return delSchedule(scheduleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('schedule/schedule/export', {
        ...this.queryParams
      }, `schedule_${new Date().getTime()}.xlsx`)
    },
    /** 格式化周期显示 */
    formatPeriod(period) {
      if (!period) return '';
      const weekMap = {
        '1': '周一',
        '2': '周二',
        '3': '周三',
        '4': '周四',
        '5': '周五',
        '6': '周六',
        '7': '周日'
      };
      return period.split(',').map(day => weekMap[day] || day).join('、');
    }
  }
};
</script>
