import request from '@/utils/request'

// 查询巡检区域列表
export function listArea(query) {
  return request({
    url: '/patrol/area/list',
    method: 'get',
    params: query
  })
}

export function areaTreeSelect(query) {
  return request({
    url: '/patrol/area/tree',
    method: 'get',
    params: query
  })
}

// 查询巡检区域详细
export function getArea(areaId) {
  return request({
    url: '/patrol/area/' + areaId,
    method: 'get'
  })
}

// 新增巡检区域
export function addArea(data) {
  return request({
    url: '/patrol/area',
    method: 'post',
    data: data
  })
}

// 修改巡检区域
export function updateArea(data) {
  return request({
    url: '/patrol/area',
    method: 'put',
    data: data
  })
}

// 删除巡检区域
export function delArea(areaId) {
  return request({
    url: '/patrol/area/' + areaId,
    method: 'delete'
  })
}
