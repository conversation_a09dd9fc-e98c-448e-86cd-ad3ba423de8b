import request from '@/utils/request'

// 查询点位检查项模板列表
export function listTemplate(query) {
  return request({
    url: '/patrol/template/list',
    method: 'get',
    params: query
  })
}

// 查询点位检查项模板详细
export function getTemplate(templateId) {
  return request({
    url: '/patrol/template/' + templateId,
    method: 'get'
  })
}

// 新增点位检查项模板
export function addTemplate(data) {
  return request({
    url: '/patrol/template',
    method: 'post',
    data: data
  })
}

// 修改点位检查项模板
export function updateTemplate(data) {
  return request({
    url: '/patrol/template',
    method: 'put',
    data: data
  })
}

// 删除点位检查项模板
export function delTemplate(templateId) {
  return request({
    url: '/patrol/template/' + templateId,
    method: 'delete'
  })
}
