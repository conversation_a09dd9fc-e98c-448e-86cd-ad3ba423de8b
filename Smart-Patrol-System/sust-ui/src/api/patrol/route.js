import request from '@/utils/request'

// 查询巡检路线列表
export function listRoute(query) {
  return request({
    url: '/patrol/route/list',
    method: 'get',
    params: query
  })
}

// 查询巡检路线详细
export function getRoute(routeId) {
  return request({
    url: '/patrol/route/' + routeId,
    method: 'get'
  })
}

// 新增巡检路线
export function addRoute(data) {
  return request({
    url: '/patrol/route',
    method: 'post',
    data: data
  })
}

// 修改巡检路线
export function updateRoute(data) {
  return request({
    url: '/patrol/route',
    method: 'put',
    data: data
  })
}

// 删除巡检路线
export function delRoute(routeId) {
  return request({
    url: '/patrol/route/' + routeId,
    method: 'delete'
  })
}

