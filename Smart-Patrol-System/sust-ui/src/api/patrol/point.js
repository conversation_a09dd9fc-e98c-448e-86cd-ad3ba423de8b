import request from '@/utils/request'

// 查询巡检点位列表
export function listPoint(query) {
  return request({
    url: '/patrol/point/list',
    method: 'get',
    params: query
  })
}

// 查询巡检点位详细
export function getPoint(pointId) {
  return request({
    url: '/patrol/point/' + pointId,
    method: 'get'
  })
}

// 新增巡检点位
export function addPoint(data) {
  return request({
    url: '/patrol/point',
    method: 'post',
    data: data
  })
}

// 修改巡检点位
export function updatePoint(data) {
  return request({
    url: '/patrol/point',
    method: 'put',
    data: data
  })
}

// 删除巡检点位
export function delPoint(pointId) {
  return request({
    url: '/patrol/point/' + pointId,
    method: 'delete'
  })
}


