import request from '@/utils/request'

// 查询巡逻队伍列表
export function listTeam(query) {
  return request({
    url: '/patrol/team/list',
    method: 'get',
    params: query
  })
}

// 查询巡逻队伍详细
export function getTeam(teamId) {
  return request({
    url: '/patrol/team/' + teamId,
    method: 'get'
  })
}

// 新增巡逻队伍
export function addTeam(data) {
  return request({
    url: '/patrol/team',
    method: 'post',
    data: data
  })
}

// 修改巡逻队伍
export function updateTeam(data) {
  return request({
    url: '/patrol/team',
    method: 'put',
    data: data
  })
}

// 删除巡逻队伍
export function delTeam(teamId) {
  return request({
    url: '/patrol/team/' + teamId,
    method: 'delete'
  })
}

// 获取可选择的用户列表
export function getTeamUserList(query) {
  return request({
    url: '/patrol/team/users',
    method: 'get',
    params: query
  })
}

// 检查巡逻队伍名称是否重复
export function checkTeamName(teamName, deptId, teamId) {
  return request({
    url: '/patrol/team/checkTeamName',
    method: 'get',
    params: {
      teamName,
      deptId,
      teamId
    }
  })
}
