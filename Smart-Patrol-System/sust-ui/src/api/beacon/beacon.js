import request from '@/utils/request'

// 查询信标管理列表
export function listBeacon(query) {
  return request({
    url: '/beacon/beacon/list',
    method: 'get',
    params: query
  })
}

// 查询信标管理详细
export function getBeacon(beaconId) {
  return request({
    url: '/beacon/beacon/' + beaconId,
    method: 'get'
  })
}

// 新增信标管理
export function addBeacon(data) {
  return request({
    url: '/beacon/beacon',
    method: 'post',
    data: data
  })
}

// 修改信标管理
export function updateBeacon(data) {
  return request({
    url: '/beacon/beacon',
    method: 'put',
    data: data
  })
}

// 删除信标管理
export function delBeacon(beaconIds) {
  // 处理批量删除的路径格式
  const ids = Array.isArray(beaconIds) ? beaconIds.join(',') : beaconIds;
  return request({
    url: '/beacon/beacon/' + ids,
    method: 'delete'
  })
}
