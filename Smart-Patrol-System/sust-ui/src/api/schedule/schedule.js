import request from '@/utils/request'

// 查询计划表列表
export function listSchedule(query) {
  return request({
    url: '/schedule/schedule/list',
    method: 'get',
    params: query
  })
}

// 查询计划表详细
export function getSchedule(scheduleId) {
  return request({
    url: '/schedule/schedule/' + scheduleId,
    method: 'get'
  })
}

// 新增计划表
export function addSchedule(data) {
  return request({
    url: '/schedule/schedule',
    method: 'post',
    data: data
  })
}

// 修改计划表
export function updateSchedule(data) {
  return request({
    url: '/schedule/schedule',
    method: 'put',
    data: data
  })
}

// 删除计划表
export function delSchedule(scheduleId) {
  return request({
    url: '/schedule/schedule/' + scheduleId,
    method: 'delete'
  })
}
