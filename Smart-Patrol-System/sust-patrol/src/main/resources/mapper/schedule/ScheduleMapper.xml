<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.schedule.mapper.ScheduleMapper">
    
    <resultMap type="Schedule" id="ScheduleResult">
        <result property="scheduleId"    column="schedule_id"    />
        <result property="routeId"    column="route_id"    />
        <result property="scheduleName"    column="schedule_name"    />
        <result property="teamId"    column="team_id"    />
        <result property="scheduledStartTime"    column="scheduled_start_time"    />
        <result property="duration"    column="duration"    />
        <result property="period"    column="period"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <resultMap type="top.sust.patrol.schedule.domain.vo.ScheduleRespVO" id="ScheduleRespResult">
        <result property="scheduleId"    column="schedule_id"    />
        <result property="routeId"    column="route_id"    />
        <result property="routeName"    column="route_name"    />
        <result property="scheduleName"    column="schedule_name"    />
        <result property="teamId"    column="team_id"    />
        <result property="teamName"    column="team_name"    />
        <result property="scheduledStartTime"    column="scheduled_start_time"    />
        <result property="duration"    column="duration"    />
        <result property="period"    column="period"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="top.sust.patrol.schedule.domain.vo.ScheduleSimpleRespVO" id="ScheduleSimpleRespResult">
        <result property="scheduleId"    column="schedule_id"    />
        <result property="scheduleName"    column="schedule_name"    />
        <result property="routeId"    column="route_id"    />
        <result property="routeName"    column="route_name"    />
        <result property="teamId"    column="team_id"    />
        <result property="teamName"    column="team_name"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectScheduleVo">
        select schedule_id, route_id, schedule_name, team_id, scheduled_start_time, duration, period, status, create_by, create_time, update_by, update_time, del_flag from patrol_schedule
    </sql>

    <sql id="selectScheduleWithJoinVo">
        select s.schedule_id, s.route_id, r.route_name, s.schedule_name, s.team_id, t.team_name,
               s.scheduled_start_time, s.duration, s.period, s.status, s.create_by, s.create_time, s.update_by, s.update_time
        from patrol_schedule s
        left join patrol_route r on s.route_id = r.route_id
        left join patrol_team t on s.team_id = t.team_id
        where s.del_flag = '0'
    </sql>

    <select id="selectScheduleList" parameterType="Schedule" resultMap="ScheduleResult">
        <include refid="selectScheduleVo"/>
        <where>  
            <if test="routeId != null "> and route_id = #{routeId}</if>
            <if test="scheduleName != null  and scheduleName != ''"> and schedule_name like concat('%', #{scheduleName}, '%')</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectScheduleByScheduleId" parameterType="Long" resultMap="ScheduleResult">
        <include refid="selectScheduleVo"/>
        where schedule_id = #{scheduleId}
    </select>

    <insert id="insertSchedule" parameterType="Schedule" useGeneratedKeys="true" keyProperty="scheduleId">
        insert into patrol_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="routeId != null">route_id,</if>
            <if test="scheduleName != null and scheduleName != ''">schedule_name,</if>
            <if test="teamId != null">team_id,</if>
            <if test="scheduledStartTime != null">scheduled_start_time,</if>
            <if test="duration != null">duration,</if>
            <if test="period != null">period,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="routeId != null">#{routeId},</if>
            <if test="scheduleName != null and scheduleName != ''">#{scheduleName},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="scheduledStartTime != null">#{scheduledStartTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="period != null">#{period},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateSchedule" parameterType="Schedule">
        update patrol_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="routeId != null">route_id = #{routeId},</if>
            <if test="scheduleName != null and scheduleName != ''">schedule_name = #{scheduleName},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="scheduledStartTime != null">scheduled_start_time = #{scheduledStartTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="period != null">period = #{period},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where schedule_id = #{scheduleId}
    </update>

    <delete id="deleteScheduleByScheduleId" parameterType="Long">
        delete from patrol_schedule where schedule_id = #{scheduleId}
    </delete>

    <delete id="deleteScheduleByScheduleIds" parameterType="String">
        delete from patrol_schedule where schedule_id in
        <foreach item="scheduleId" collection="array" open="(" separator="," close=")">
            #{scheduleId}
        </foreach>
    </delete>

    <select id="selectScheduleRespList" parameterType="top.sust.patrol.schedule.domain.vo.ScheduleListReqVO" resultMap="ScheduleRespResult">
        <include refid="selectScheduleWithJoinVo"/>
        <if test="scheduleName != null and scheduleName != ''">
            and s.schedule_name like concat('%', #{scheduleName}, '%')
        </if>
        <if test="routeId != null">
            and s.route_id = #{routeId}
        </if>
        <if test="routeName != null and routeName != ''">
            and r.route_name like concat('%', #{routeName}, '%')
        </if>
        <if test="teamId != null">
            and s.team_id = #{teamId}
        </if>
        <if test="teamName != null and teamName != ''">
            and t.team_name like concat('%', #{teamName}, '%')
        </if>
        <if test="status != null and status != ''">
            and s.status = #{status}
        </if>
        <if test="period != null and period != ''">
            and s.period like concat('%', #{period}, '%')
        </if>
        order by s.create_time desc
    </select>

    <select id="selectScheduleRespByScheduleId" parameterType="Long" resultMap="ScheduleRespResult">
        <include refid="selectScheduleWithJoinVo"/>
        and s.schedule_id = #{scheduleId}
    </select>

    <select id="selectScheduleSimpleList" resultMap="ScheduleSimpleRespResult">
        <include refid="selectScheduleWithJoinVo"/>
        and s.status = '0'
        order by s.schedule_name
    </select>

    <select id="selectEnabledSchedulesByDayOfWeek" parameterType="Integer" resultMap="ScheduleResult">
        <include refid="selectScheduleVo"/>
        where del_flag = '0' and status = '0' and find_in_set(#{dayOfWeek}, period)
    </select>
</mapper>