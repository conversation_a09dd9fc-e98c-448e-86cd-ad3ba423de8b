<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.route.mapper.PatrolRouteMapper">
    
    <resultMap type="PatrolRoute" id="PatrolRouteResult">
        <result property="routeId"    column="route_id"    />
        <result property="routeName"    column="route_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="routeName"    column="route_name"    />
    </resultMap>

    <sql id="selectPatrolRouteVo">
        select route_id, route_name, dept_id, status, create_by, create_time, update_by, update_time, del_flag from patrol_route
    </sql>

<select id="selectPatrolRouteList" parameterType="PatrolRoute" resultMap="PatrolRouteResult">
    select
    pr.route_id as routeId,
    pr.route_name as routeName,
    pr.dept_id as deptId,
    sd.dept_name as deptName,
    pr.status,
    pr.create_by as createBy,
    pr.create_time as createTime,
    pr.update_by as updateBy,
    pr.update_time as updateTime
    from patrol_route pr
    left join sys_dept sd on pr.dept_id = sd.dept_id
    <where>
        pr.del_flag = '0'
        <if test="routeName != null and routeName != ''"> and pr.route_name like concat('%', #{routeName}, '%')</if>
        <if test="deptId != null and deptId != 0">
            AND (pr.dept_id = #{deptId} OR pr.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
        </if>
        <if test="status != null and status != ''"> and pr.status = #{status}</if>
    </where>
    <!-- 默认排序，若有PageHelper的orderBy会自动覆盖 -->
    order by pr.create_time desc
</select>

    
    <select id="selectPatrolRouteByRouteId" parameterType="Long" resultType="top.sust.patrol.route.domain.vo.PatrolRouteRespVO">
        select
            pr.route_id as routeId,
            pr.route_name as routeName,
            pr.dept_id as deptId,
            sd.dept_name as deptName,
            pr.status,
            pr.create_by as createBy,
            pr.create_time as createTime,
            pr.update_by as updateBy,
            pr.update_time as updateTime
        from patrol_route pr
                 left join sys_dept sd on pr.dept_id = sd.dept_id
        where route_id = #{routeId} and pr.del_flag = '0'
    </select>

    <insert id="insertPatrolRoute" parameterType="PatrolRoute" useGeneratedKeys="true" keyProperty="routeId">
        insert into patrol_route
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="routeName != null and routeName != ''">route_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="routeName != null and routeName != ''">#{routeName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePatrolRoute" parameterType="PatrolRoute">
        update patrol_route
        <trim prefix="SET" suffixOverrides=",">
            <if test="routeName != null and routeName != ''">route_name = #{routeName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where route_id = #{routeId}
    </update>

    <delete id="deletePatrolRouteByRouteId" parameterType="Long">
        delete from patrol_route where route_id = #{routeId}
    </delete>

    <delete id="deletePatrolRouteByRouteIds" parameterType="String">
        delete from patrol_route where route_id in 
        <foreach item="routeId" collection="array" open="(" separator="," close=")">
            #{routeId}
        </foreach>
    </delete>

    <!-- 查询巡检路线列表（包含部门名称） -->
    <select id="selectPatrolRouteListWithDept" parameterType="PatrolRoute" resultType="top.sust.patrol.route.domain.vo.PatrolRouteRespVO">
        select 
            pr.route_id as routeId,
            pr.route_name as routeName,
            pr.dept_id as deptId,
            sd.dept_name as deptName,
            pr.status,
            pr.create_by as createBy,
            pr.create_time as createTime,
            pr.update_by as updateBy,
            pr.update_time as updateTime
        from patrol_route pr
        left join sys_dept sd on pr.dept_id = sd.dept_id
        <where>
            pr.del_flag = '0'
            <if test="routeName != null and routeName != ''"> and pr.route_name like concat('%', #{routeName}, '%')</if>
            <if test="deptId != null and deptId != 0">
                AND (pr.dept_id = #{deptId} OR pr.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="status != null and status != ''"> and pr.status = #{status}</if>
            <if test="routeId != null"> and pr.route_id = #{routeId}</if>
        </where>
        <!-- 默认排序，若有PageHelper的orderBy会自动覆盖 -->
        order by pr.create_time desc
    </select>

    <!-- 根据路线ID查询关联的点位信息 -->
    <select id="selectPatrolPointsByRouteId" parameterType="Long" resultType="top.sust.patrol.route.domain.vo.PatrolRouteRespVO$PatrolPointInfo">
        select 
            pp.point_id as pointId,
            pp.point_name as pointName,
            prp.point_order as pointOrder,
            prp.status
        from patrol_route_point prp
        inner join patrol_point pp on prp.point_id = pp.point_id
        where prp.route_id = #{routeId}
        and pp.del_flag = '0'
        order by prp.point_order asc
    </select>

    <!-- 批量插入路线点位关联关系 -->
    <insert id="batchInsertRoutePoints">
        insert into patrol_route_point (route_id, point_id, point_order, status)
        values
        <foreach collection="pointIds" item="pointId" index="index" separator=",">
            (#{routeId}, #{pointId}, #{index}, #{status})
        </foreach>
    </insert>

    <!-- 查询巡检路线简单列表 -->
    <select id="selectSimplePatrolRouteList" parameterType="PatrolRoute" resultType="top.sust.patrol.route.domain.vo.PatrolRouteSimpleRespVO">
        select 
            pr.route_id as routeId,
            pr.route_name as routeName,
            pr.dept_id as deptId,
            sd.dept_name as deptName,
            pr.status
        from patrol_route pr
        left join sys_dept sd on pr.dept_id = sd.dept_id
        <where>
            pr.del_flag = '0'
            <if test="routeName != null and routeName != ''"> and pr.route_name like concat('%', #{routeName}, '%')</if>
            <if test="deptId != null "> and pr.dept_id = #{deptId}</if>
            <if test="status != null and status != ''"> and pr.status = #{status}</if>
        </where>
        order by pr.create_time desc
    </select>

    <!-- 批量插入路线点位关联关系（带详细信息） -->
    <insert id="batchInsertRoutePointsWithDetails">
        insert into patrol_route_point (route_id, point_id, point_order, status)
        values
        <foreach collection="routePoints" item="routePoint" separator=",">
            (#{routeId}, #{routePoint.pointId}, #{routePoint.pointOrder}, #{routePoint.status})
        </foreach>
    </insert>

    <!-- 删除路线点位关联关系 -->
    <delete id="deleteRoutePointsByRouteId" parameterType="Long">
        delete from patrol_route_point where route_id = #{routeId}
    </delete>

    <!-- 检查路线名称是否已存在 -->
    <select id="checkRouteNameUnique" parameterType="map" resultType="int">
        select count(1) from patrol_route
        where route_name = #{routeName}
        and del_flag = '0'
        <if test="routeId != null and routeId != 0">
            and route_id != #{routeId}
        </if>
    </select>
</mapper>