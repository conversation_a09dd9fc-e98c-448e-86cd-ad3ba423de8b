<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.team.mapper.PatrolTeamMemberMapper">
    
    <delete id="deletePatrolTeamMemberByTeamId" parameterType="Long">
        delete from patrol_team_member where team_id = #{teamId}
    </delete>
    
    <insert id="batchPatrolTeamMember">
        insert into patrol_team_member(team_id, user_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.teamId}, #{item.userId})
        </foreach>
    </insert>
    
    <select id="selectUserIdsByTeamId" parameterType="Long" resultType="Long">
        select user_id from patrol_team_member where team_id = #{teamId}
    </select>
    
</mapper>