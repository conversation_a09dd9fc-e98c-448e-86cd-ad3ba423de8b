<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.team.mapper.PatrolTeamMapper">
    
    <resultMap type="PatrolTeam" id="PatrolTeamResult">
        <id     property="teamId"       column="team_id"      />
        <result property="teamName"     column="team_name"    />
        <result property="deptId"       column="dept_id"      />
        <result property="status"       column="status"       />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="delFlag"      column="del_flag"     />
        <result property="memberCount"  column="member_count" />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <resultMap type="PatrolTeam" id="PatrolTeamMemberResult" extends="PatrolTeamResult">
        <collection property="members" javaType="java.util.List" ofType="top.sust.common.core.domain.entity.SysUser">
            <id property="userId" column="user_id"/>
            <result property="userName" column="user_name"/>
            <result property="nickName" column="nick_name"/>
            <result property="email" column="email"/>
            <result property="phonenumber" column="phonenumber"/>
            <result property="sex" column="sex"/>
            <result property="avatar" column="avatar"/>
            <result property="status" column="user_status"/>
            <association property="dept" javaType="top.sust.common.core.domain.entity.SysDept">
                <result property="deptId" column="user_dept_id"/>
                <result property="deptName" column="user_dept_name"/>
            </association>
        </collection>
    </resultMap>

    <sql id="selectPatrolTeamVo">
        select t.team_id, t.team_name, t.dept_id, t.status, t.create_by, t.create_time, t.update_by, t.update_time, t.del_flag,
               COALESCE(tm.member_count, 0) as member_count,d.dept_name
        from patrol_team t
        left join (
            select team_id, count(*) as member_count 
            from patrol_team_member 
            group by team_id
        ) tm on t.team_id = tm.team_id
        left join sys_dept d on t.dept_id = d.dept_id

    </sql>



    <sql id="selectPatrolTeamMemberVo">
        select t.team_id, t.team_name, t.dept_id, t.status, t.create_by, t.create_time, t.update_by, t.update_time, t.del_flag,
               u.user_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.sex, u.avatar, u.status as user_status,
               d.dept_id as user_dept_id, d.dept_name as user_dept_name
        from patrol_team t
                 left join patrol_team_member tm on t.team_id = tm.team_id
                 left join sys_user u on tm.user_id = u.user_id
                 left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPatrolTeamList" parameterType="PatrolTeam" resultMap="PatrolTeamResult">
        <include refid="selectPatrolTeamVo"/>
        <where>
            t.del_flag = '0'
            <if test="teamName != null and teamName != ''"> and t.team_name like concat('%', #{teamName}, '%')</if>

            <if test="deptId != null and deptId != 0">
                AND (t.dept_id = #{deptId} OR t.dept_id IN ( SELECT a.dept_id FROM sys_dept a WHERE find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="status != null and status != ''"> and t.status = #{status}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by t.team_id desc
    </select>
    
    <select id="selectPatrolTeamByTeamId" parameterType="Long" resultMap="PatrolTeamMemberResult">
        <include refid="selectPatrolTeamMemberVo"/>
        where t.team_id = #{teamId} and t.del_flag = '0'
    </select>

    <insert id="insertPatrolTeam" parameterType="PatrolTeam" useGeneratedKeys="true" keyProperty="teamId">
        insert into patrol_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">#{teamName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePatrolTeam" parameterType="PatrolTeam">
        update patrol_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name = #{teamName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where team_id = #{teamId}
    </update>

    <delete id="deletePatrolTeamByTeamId" parameterType="Long">
        delete from patrol_team where team_id = #{teamId}
    </delete>

    <delete id="deletePatrolTeamByTeamIds" parameterType="String">
        update patrol_team set del_flag = '2' where team_id in
        <foreach item="teamId" collection="array" open="(" separator="," close=")">
            #{teamId}
        </foreach>
    </delete>

    <select id="checkTeamNameDuplicate" resultType="int">
        select count(1) from patrol_team
        where team_name = #{teamName}
        and dept_id = #{deptId}
        and del_flag = '0'
        <if test="teamId != null">
            and team_id != #{teamId}
        </if>
    </select>
</mapper>
