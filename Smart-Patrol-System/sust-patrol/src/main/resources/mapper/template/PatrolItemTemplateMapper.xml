<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.template.mapper.PatrolItemTemplateMapper">
    
    <resultMap type="PatrolItemTemplate" id="PatrolItemTemplateResult">
        <result property="templateId"    column="template_id"    />
        <result property="templateName"    column="template_name"    />
        <result property="description"    column="description"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPatrolItemTemplateVo">
        select template_id, template_name, description,type, create_by, create_time, update_by, update_time from patrol_item_template
    </sql>

    <select id="selectPatrolItemTemplateList" parameterType="PatrolItemTemplateListReqVO" resultMap="PatrolItemTemplateResult">
        <include refid="selectPatrolItemTemplateVo"/>
        <where>  
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>  <!-- 新增类型查询条件 -->
        </where>
    </select>
    
    <select id="selectPatrolItemTemplateByTemplateId" parameterType="Long" resultMap="PatrolItemTemplateResult">
        <include refid="selectPatrolItemTemplateVo"/>
        where template_id = #{templateId}
    </select>

    <insert id="insertPatrolItemTemplate" parameterType="PatrolItemTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into patrol_item_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="type != null and type != ''">type,</if>  <!-- 新增类型字段插入 -->
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">#{templateName},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="type != null and type != ''">#{type},</if>  <!-- 新增类型值插入 -->
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePatrolItemTemplate" parameterType="PatrolItemTemplate">
        update patrol_item_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="type != null and type != ''">type = #{type},</if>  <!-- 新增类型字段更新 -->
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deletePatrolItemTemplateByTemplateId" parameterType="Long">
        delete from patrol_item_template where template_id = #{templateId}
    </delete>

    <delete id="deletePatrolItemTemplateByTemplateIds" parameterType="String">
        delete from patrol_item_template where template_id in 
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
</mapper>