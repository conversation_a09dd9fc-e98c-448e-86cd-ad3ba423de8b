<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.beacon.mapper.PatrolBeaconMapper">
    
    <resultMap type="PatrolBeacon" id="PatrolBeaconResult">
        <result property="beaconId"    column="beacon_id"    />
        <result property="beaconName"    column="beacon_name"    />
        <result property="beaconMac"    column="beacon_mac"    />
        <result property="beaconUuid"    column="beacon_uuid"    />
        <result property="beaconMajor"    column="beacon_major"    />
        <result property="beaconMinor"    column="beacon_minor"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectPatrolBeaconVo">
        select beacon_id, beacon_name, beacon_mac, beacon_uuid, beacon_major, beacon_minor, create_by, create_time, update_by, update_time, status, del_flag from patrol_beacon
    </sql>

    <select id="selectPatrolBeaconList" parameterType="PatrolBeaconListReqVO" resultMap="PatrolBeaconResult">
        <include refid="selectPatrolBeaconVo"/>
        <where>  
            <if test="beaconName != null  and beaconName != ''"> and beacon_name like concat('%', #{beaconName}, '%')</if>
            <if test="beaconMac != null  and beaconMac != ''"> and beacon_mac like concat('%', #{beaconMac}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPatrolBeaconByBeaconId" parameterType="Long" resultMap="PatrolBeaconResult">
        <include refid="selectPatrolBeaconVo"/>
        where beacon_id = #{beaconId}
    </select>

    <insert id="insertPatrolBeacon" parameterType="PatrolBeacon" useGeneratedKeys="true" keyProperty="beaconId">
        insert into patrol_beacon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="beaconName != null and beaconName != ''">beacon_name,</if>
            <if test="beaconMac != null">beacon_mac,</if>
            <if test="beaconUuid != null">beacon_uuid,</if>
            <if test="beaconMajor != null">beacon_major,</if>
            <if test="beaconMinor != null">beacon_minor,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="beaconName != null and beaconName != ''">#{beaconName},</if>
            <if test="beaconMac != null">#{beaconMac},</if>
            <if test="beaconUuid != null">#{beaconUuid},</if>
            <if test="beaconMajor != null">#{beaconMajor},</if>
            <if test="beaconMinor != null">#{beaconMinor},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePatrolBeacon" parameterType="PatrolBeacon">
        update patrol_beacon
        <trim prefix="SET" suffixOverrides=",">
            <if test="beaconName != null and beaconName != ''">beacon_name = #{beaconName},</if>
            <if test="beaconMac != null">beacon_mac = #{beaconMac},</if>
            <if test="beaconUuid != null">beacon_uuid = #{beaconUuid},</if>
            <if test="beaconMajor != null">beacon_major = #{beaconMajor},</if>
            <if test="beaconMinor != null">beacon_minor = #{beaconMinor},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where beacon_id = #{beaconId}
    </update>

    <delete id="deletePatrolBeaconByBeaconId" parameterType="Long">
        delete from patrol_beacon where beacon_id = #{beaconId}
    </delete>

    <delete id="deletePatrolBeaconByBeaconIds" parameterType="String">
        delete from patrol_beacon where beacon_id in 
        <foreach item="beaconId" collection="array" open="(" separator="," close=")">
            #{beaconId}
        </foreach>
    </delete>
</mapper>