<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.task.mapper.TaskMapper">
    
    <resultMap type="Task" id="TaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="scheduleId"    column="schedule_id"    />
        <result property="routeId"    column="route_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskType"    column="task_type"    />
        <result property="teamId"    column="team_id"    />
        <result property="scheduledPatrolUsers"    column="scheduled_patrol_users"    />
        <result property="scheduledStartTime"    column="scheduled_start_time"    />
        <result property="duration"    column="duration"    />
        <result property="actualCompleteTime"    column="actual_complete_time"    />
        <result property="status"    column="status"    />
        <result property="normalStatus"    column="normal_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTaskVo">
        select task_id, schedule_id, route_id, task_name, task_type, team_id, scheduled_patrol_users, scheduled_start_time, duration, actual_complete_time, status, normal_status, create_by, create_time, update_by, update_time, del_flag from patrol_task
    </sql>

    <select id="selectTaskList" parameterType="Task" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        <where>  
            <if test="scheduleId != null "> and schedule_id = #{scheduleId}</if>
            <if test="routeId != null "> and route_id = #{routeId}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskType != null  and taskType != ''"> and task_type = #{taskType}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="normalStatus != null  and normalStatus != ''"> and normal_status = #{normalStatus}</if>
        </where>
    </select>
    
    <select id="selectTaskByTaskId" parameterType="Long" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertTask" parameterType="Task" useGeneratedKeys="true" keyProperty="taskId">
        insert into patrol_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scheduleId != null">schedule_id,</if>
            <if test="routeId != null">route_id,</if>
            <if test="taskName != null">task_name,</if>
            <if test="taskType != null">task_type,</if>
            <if test="teamId != null">team_id,</if>
            <if test="scheduledPatrolUsers != null">scheduled_patrol_users,</if>
            <if test="scheduledStartTime != null">scheduled_start_time,</if>
            <if test="duration != null">duration,</if>
            <if test="actualCompleteTime != null">actual_complete_time,</if>
            <if test="status != null">status,</if>
            <if test="normalStatus != null">normal_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scheduleId != null">#{scheduleId},</if>
            <if test="routeId != null">#{routeId},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="scheduledPatrolUsers != null">#{scheduledPatrolUsers},</if>
            <if test="scheduledStartTime != null">#{scheduledStartTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="actualCompleteTime != null">#{actualCompleteTime},</if>
            <if test="status != null">#{status},</if>
            <if test="normalStatus != null">#{normalStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTask" parameterType="Task">
        update patrol_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="scheduleId != null">schedule_id = #{scheduleId},</if>
            <if test="routeId != null">route_id = #{routeId},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="scheduledPatrolUsers != null">scheduled_patrol_users = #{scheduledPatrolUsers},</if>
            <if test="scheduledStartTime != null">scheduled_start_time = #{scheduledStartTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="actualCompleteTime != null">actual_complete_time = #{actualCompleteTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="normalStatus != null">normal_status = #{normalStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteTaskByTaskId" parameterType="Long">
        delete from patrol_task where task_id = #{taskId}
    </delete>

    <delete id="deleteTaskByTaskIds" parameterType="String">
        delete from patrol_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>