<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.area.mapper.PatrolAreaMapper">
    
    <resultMap type="PatrolArea" id="PatrolAreaResult">
        <result property="areaId"    column="area_id"    />
        <result property="areaPid"    column="area_pid"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="areaName"    column="area_name"    />
        <result property="orderNum"    column="order_num"    />
        <result property="deptId"    column="dept_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectPatrolAreaVo">
        select area_id, area_pid, ancestors, area_name, order_num, dept_id, status, create_by, create_time, update_by, update_time, del_flag from patrol_area
    </sql>

    <select id="selectChildrenAreaById" parameterType="Long" resultMap="PatrolAreaResult">
        select * from patrol_area where find_in_set(#{areaId}, ancestors)
    </select>

    <select id="hasChildByAreaId" parameterType="Long" resultType="int">
        select count(1) from patrol_area
        where del_flag = '0' and area_pid = #{areaId} limit 1
    </select>

    <select id="selectPatrolAreaList" parameterType="PatrolAreaListReqVO" resultMap="PatrolAreaResult">
        <include refid="selectPatrolAreaVo"/>
        <where>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="deptId != null "> and (dept_id in (select dept_id from sys_dept where find_in_set(#{deptId}, ancestors)) or dept_id=#{deptId})</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by area_pid, order_num
    </select>
    
    <select id="selectPatrolAreaByAreaId" parameterType="Long" resultMap="PatrolAreaResult">
        <include refid="selectPatrolAreaVo"/>
        where area_id = #{areaId}
    </select>

    <insert id="insertPatrolArea" parameterType="PatrolArea" useGeneratedKeys="true" keyProperty="areaId">
        insert into patrol_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="areaPid != null">area_pid,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
            <if test="areaName != null and areaName != ''">area_name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="areaPid != null">#{areaPid},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
            <if test="areaName != null and areaName != ''">#{areaName},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePatrolArea" parameterType="PatrolArea">
        update patrol_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaPid != null">area_pid = #{areaPid},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="areaName != null and areaName != ''">area_name = #{areaName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where area_id = #{areaId}
    </update>

    <update id="updateAreaChildren" parameterType="java.util.List">
        update patrol_area set ancestors =
        <foreach collection="areas" item="item" index="index"
                 separator=" " open="case area_id" close="end">
            when #{item.areaId} then #{item.ancestors}
        </foreach>
        where area_id in
        <foreach collection="areas" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.areaId}
        </foreach>
    </update>

    <delete id="deletePatrolAreaByAreaId" parameterType="Long">
        delete from patrol_area where area_id = #{areaId}
    </delete>

    <delete id="deletePatrolAreaByAreaIds" parameterType="String">
        delete from patrol_area where area_id in 
        <foreach item="areaId" collection="array" open="(" separator="," close=")">
            #{areaId}
        </foreach>
    </delete>
</mapper>