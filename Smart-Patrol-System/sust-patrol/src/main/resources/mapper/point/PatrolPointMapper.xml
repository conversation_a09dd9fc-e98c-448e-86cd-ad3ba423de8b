<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.sust.patrol.point.mapper.PatrolPointMapper">
    
    <resultMap type="PatrolPoint" id="PatrolPointResult">
        <result property="pointId"    column="point_id"    />
        <result property="pointName"    column="point_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="beaconId"    column="beacon_id"    />
        <result property="beaconName"    column="beacon_name"    />
        <result property="longtitude"    column="longtitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="locationX"    column="location_x"    />
        <result property="locationY"    column="location_y"    />
        <result property="locationLayer"    column="location_layer"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <resultMap id="PatrolPointPatrolPointItemResult" type="PatrolPoint" extends="PatrolPointResult">
        <collection property="patrolPointItemList" ofType="PatrolPointItem" column="point_id" select="selectPatrolPointItemList" />
    </resultMap>

    <resultMap type="PatrolPointItem" id="PatrolPointItemResult">
        <result property="itemId"    column="item_id"    />
        <result property="itemName"    column="item_name"    />
        <result property="description"    column="description"    />
        <result property="pointId"    column="point_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectPatrolPointVo">
        select p.point_id, p.point_name, p.area_id, p.beacon_id, p.longtitude, p.latitude, p.location_x, p.location_y, p.location_layer, p.status, p.create_by, p.create_time, p.update_by, p.update_time, p.del_flag,
               b.beacon_name
        from patrol_point p
        left join patrol_beacon b on p.beacon_id = b.beacon_id
    </sql>

    <select id="selectPatrolPointList" parameterType="PatrolPointListReqVO" resultMap="PatrolPointResult">
        <include refid="selectPatrolPointVo"/>
        <where>  
            <if test="pointName != null  and pointName != ''"> and p.point_name like concat('%', #{pointName}, '%')</if>
            <if test="areaId != null "> and p.area_id in (select area_id from patrol_area where find_in_set(#{areaId}, ancestors) or area_id = #{areaId})</if>
            <if test="beaconName != null and beaconName != ''"> and b.beacon_name like concat('%', #{beaconName}, '%')</if>
            <if test="locationLayer != null "> and p.location_layer = #{locationLayer}</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPatrolPointByPointId" parameterType="Long" resultMap="PatrolPointPatrolPointItemResult">
        <include refid="selectPatrolPointVo"/>
        where p.point_id = #{pointId}
    </select>

    <select id="selectPatrolPointByBeaconId" parameterType="Long" resultMap="PatrolPointResult">
        <include refid="selectPatrolPointVo"/>
        where p.beacon_id = #{beaconId}
    </select>

    <select id="selectPatrolPointItemList" resultMap="PatrolPointItemResult">
        select item_id, item_name, description, point_id, status, create_by, create_time, update_by, update_time, del_flag
        from patrol_point_item
        where point_id = #{point_id}
    </select>

    <insert id="insertPatrolPoint" parameterType="PatrolPoint" useGeneratedKeys="true" keyProperty="pointId">
        insert into patrol_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">point_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="beaconId != null">beacon_id,</if>
            <if test="longtitude != null">longtitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="locationX != null">location_x,</if>
            <if test="locationY != null">location_y,</if>
            <if test="locationLayer != null">location_layer,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">#{pointName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="beaconId != null">#{beaconId},</if>
            <if test="longtitude != null">#{longtitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="locationX != null">#{locationX},</if>
            <if test="locationY != null">#{locationY},</if>
            <if test="locationLayer != null">#{locationLayer},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePatrolPoint" parameterType="PatrolPoint">
        update patrol_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">point_name = #{pointName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="beaconId != null">beacon_id = #{beaconId},</if>
            <if test="longtitude != null">longtitude = #{longtitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="locationX != null">location_x = #{locationX},</if>
            <if test="locationY != null">location_y = #{locationY},</if>
            <if test="locationLayer != null">location_layer = #{locationLayer},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where point_id = #{pointId}
    </update>

    <delete id="deletePatrolPointByPointIds" parameterType="String">
        delete from patrol_point where point_id in 
        <foreach item="pointId" collection="array" open="(" separator="," close=")">
            #{pointId}
        </foreach>
    </delete>
    
    <delete id="deletePatrolPointItemByPointIds" parameterType="String">
        delete from patrol_point_item where point_id in 
        <foreach item="pointId" collection="array" open="(" separator="," close=")">
            #{pointId}
        </foreach>
    </delete>

    <delete id="deletePatrolPointItemByPointId" parameterType="Long">
        delete from patrol_point_item where point_id = #{pointId}
    </delete>

    <insert id="batchPatrolPointItem">
        insert into patrol_point_item( item_id, item_name, description, point_id, status, create_by, create_time, update_by, update_time, del_flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.itemId}, #{item.itemName}, #{item.description}, #{item.pointId}, #{item.status}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.delFlag})
        </foreach>
    </insert>

    <!-- 检查点位是否被巡检路线使用 -->
    <select id="countRoutesUsingPoints" parameterType="Long[]" resultType="int">
        select count(distinct prp.route_id)
        from patrol_route_point prp
        inner join patrol_route pr on prp.route_id = pr.route_id
        where prp.point_id in
        <foreach item="pointId" collection="array" open="(" separator="," close=")">
            #{pointId}
        </foreach>
        and pr.del_flag = '0'
    </select>
</mapper>