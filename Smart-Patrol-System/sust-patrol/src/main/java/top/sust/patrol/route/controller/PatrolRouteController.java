package top.sust.patrol.route.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.enums.BusinessType;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.patrol.route.domain.PatrolRoute;
import top.sust.patrol.route.domain.vo.PatrolRouteSaveReqVO;
import top.sust.patrol.route.domain.vo.PatrolRouteListReqVO;
import top.sust.patrol.route.domain.vo.PatrolRouteSimpleRespVO;
import top.sust.patrol.route.domain.vo.PatrolRouteRespVO;
import top.sust.patrol.route.service.IPatrolRouteService;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.common.core.page.TableDataInfo;

/**
 * 巡检路线接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Api(tags = "巡检路线管理接口")
@RestController
@RequestMapping("/patrol/route")
public class PatrolRouteController extends BaseController
{
    @Autowired
    private IPatrolRouteService patrolRouteService;

    /**
     * 查询巡检路线列表
     */
    @ApiOperation(value = "查询巡检路线列表")
    @PreAuthorize("@ss.hasPermi('patrol:route:list')")
    @GetMapping("/list")
    public TableDataInfo list(PatrolRouteListReqVO listReqVO)
    {
        startPage();
        List<PatrolRouteRespVO> list = patrolRouteService.listPatrolRoutesWithDetails(listReqVO);
        return getDataTable(list);
    }

    /**
     * 查询巡检路线简单列表
     */
    @ApiOperation(value = "查询巡检路线简单列表")
    @PreAuthorize("@ss.hasPermi('patrol:route:list')")
    @GetMapping("/simple-list")
    public R<List<PatrolRouteSimpleRespVO>> getSimpleList(PatrolRouteListReqVO listReqVO)
    {
        List<PatrolRouteSimpleRespVO> list = patrolRouteService.listSimplePatrolRoutes(listReqVO);
        return R.ok(list);
    }

    /**
     * 导出巡检路线列表
     */
    @ApiOperation(value = "导出巡检路线列表")
    @PreAuthorize("@ss.hasPermi('patrol:route:export')")
    @Log(title = "巡检路线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolRouteListReqVO listReqVO)
    {
        // 导出时不分页，获取所有数据
        List<PatrolRouteRespVO> list = patrolRouteService.listPatrolRoutesWithDetails(listReqVO);
        ExcelUtil<PatrolRouteRespVO> util = new ExcelUtil<PatrolRouteRespVO>(PatrolRouteRespVO.class);
        util.exportExcel(response, list, "巡检路线数据");
    }

    /**
     * 获取巡检路线详细信息
     */
    @ApiOperation(value = "获取巡检路线详细信息")
    @PreAuthorize("@ss.hasPermi('patrol:route:query')")
    @GetMapping(value = "/{routeId}")
    public R<PatrolRouteRespVO> getInfo(@PathVariable("routeId") Long routeId)
    {
        return R.ok(patrolRouteService.getPatrolRouteWithDetails(routeId));
    }

    /**
     * 新增巡检路线
     */
    @ApiOperation(value = "新增巡检路线")
    @PreAuthorize("@ss.hasPermi('patrol:route:add')")
    @Log(title = "巡检路线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PatrolRouteSaveReqVO saveReqVO)
    {
        // 校验路线名称唯一性
        if (!patrolRouteService.validateRouteNameUnique(saveReqVO)) {
            return AjaxResult.error("新增路线'" + saveReqVO.getRouteName() + "'失败，路线名称已存在");
        }

        // 校验点位是否重复
        if (!patrolRouteService.validateRoutePointsUnique(saveReqVO)) {
            return AjaxResult.error("新增路线'" + saveReqVO.getRouteName() + "'失败，路线中存在重复的点位");
        }

        return toAjax(patrolRouteService.savePatrolRoute(saveReqVO));
    }

    /**
     * 修改巡检路线
     */
    @ApiOperation(value = "修改巡检路线")
    @PreAuthorize("@ss.hasPermi('patrol:route:edit')")
    @Log(title = "巡检路线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PatrolRouteSaveReqVO saveReqVO)
    {
        // 校验路线名称唯一性
        if (!patrolRouteService.validateRouteNameUnique(saveReqVO)) {
            return AjaxResult.error("修改路线'" + saveReqVO.getRouteName() + "'失败，路线名称已存在");
        }
        // 校验点位是否重复
        if (!patrolRouteService.validateRoutePointsUnique(saveReqVO)) {
            return AjaxResult.error("修改路线'" + saveReqVO.getRouteName() + "'失败，路线中存在重复的点位");
        }

        return toAjax(patrolRouteService.updatePatrolRoute(saveReqVO));
    }

    /**
     * 删除巡检路线
     */
    @ApiOperation(value = "删除巡检路线")
    @PreAuthorize("@ss.hasPermi('patrol:route:remove')")
    @Log(title = "巡检路线", businessType = BusinessType.DELETE)
	@DeleteMapping("/{routeIds}")
    public AjaxResult remove(@PathVariable Long[] routeIds)
    {
        return toAjax(patrolRouteService.deletePatrolRouteByRouteIds(routeIds));
    }


}
