package top.sust.patrol.task.service;

import java.util.List;
import top.sust.patrol.task.domain.Task;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 任务计划demoService接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface ITaskService extends IService<Task> {
    /**
     * 查询任务计划demo
     *
     * @param taskId 任务计划demo主键
     * @return 任务计划demo
     */
    public Task selectTaskByTaskId(Long taskId);

    /**
     * 查询任务计划demo列表
     *
     * @param task 任务计划demo
     * @return 任务计划demo集合
     */
    public List<Task> selectTaskList(Task task);

    /**
     * 新增任务计划demo
     *
     * @param task 任务计划demo
     * @return 结果
     */
    public int insertTask(Task task);

    /**
     * 修改任务计划demo
     *
     * @param task 任务计划demo
     * @return 结果
     */
    public int updateTask(Task task);

    /**
     * 批量删除任务计划demo
     *
     * @param taskIds 需要删除的任务计划demo主键集合
     * @return 结果
     */
    public int deleteTaskByTaskIds(Long[] taskIds);

    /**
     * 删除任务计划demo信息
     *
     * @param taskId 任务计划demo主键
     * @return 结果
     */
    public int deleteTaskByTaskId(Long taskId);
}
