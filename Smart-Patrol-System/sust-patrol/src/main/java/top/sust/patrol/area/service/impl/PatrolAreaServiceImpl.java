package top.sust.patrol.area.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import top.sust.common.core.domain.TreeSelect;
import top.sust.common.exception.ServiceException;
import top.sust.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.sust.common.utils.SecurityUtils;
import top.sust.common.utils.StringUtils;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.patrol.area.domain.vo.PatrolAreaSaveReqVO;
import top.sust.patrol.area.domain.vo.PatrolAreaTreeSelect;
import top.sust.patrol.area.domain.vo.PatrolAreaListReqVO;
import top.sust.patrol.area.mapper.PatrolAreaMapper;
import top.sust.patrol.area.domain.PatrolArea;
import top.sust.patrol.area.service.IPatrolAreaService;
import top.sust.patrol.point.domain.PatrolPoint;
import top.sust.patrol.point.mapper.PatrolPointMapper;

/**
 * 巡检区域Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class PatrolAreaServiceImpl extends ServiceImpl<PatrolAreaMapper, PatrolArea> implements IPatrolAreaService {
    @Autowired
    private PatrolAreaMapper patrolAreaMapper;

    @Autowired
    private PatrolPointMapper patrolPointMapper;

    /**
     * 查询巡检区域
     *
     * @param areaId 巡检区域主键
     * @return 巡检区域
     */
    @Override
    public PatrolArea selectPatrolAreaByAreaId(Long areaId) {
        return patrolAreaMapper.selectPatrolAreaByAreaId(areaId);
    }

    @Override
    public boolean hasChildByAreaId(Long areaId) {
        int result = patrolAreaMapper.hasChildByAreaId(areaId);
        return result > 0;
    }

    @Override
    public List<TreeSelect> selectAreaTreeList(PatrolAreaListReqVO patrolArea) {
        return buildAreaTreeSelect(selectPatrolAreaList(patrolArea));
    }

    public List<TreeSelect> buildAreaTreeSelect(List<PatrolArea> areas) {
        List<PatrolArea> areaTrees = buildAreaTree(areas);
        return areaTrees.stream().map(PatrolAreaTreeSelect::new).collect(Collectors.toList());
    }

    public List<PatrolArea> buildAreaTree(List<PatrolArea> areas) {
        List<PatrolArea> returnList = new ArrayList<PatrolArea>();
        List<Long> tempList = areas.stream().map(PatrolArea::getAreaId).collect(Collectors.toList());
        for (PatrolArea area : areas)
        {
            // 如果是顶级节点(pid=0), 遍历该节点的所有子节点
            if (!tempList.contains(area.getAreaPid()))
            {
                recursionFn(areas, area);
                returnList.add(area);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = areas;
        }
        return returnList;
    }

    private void recursionFn(List<PatrolArea> list, PatrolArea t) {
        // 得到子节点列表
        List<PatrolArea> childList = getChildList(list, t);
        t.setChildren(childList);
        for (PatrolArea tChild : childList)
        {
            if (getChildList(list, tChild).size() > 0)
            {
                recursionFn(list, tChild);
            }
        }
    }

    private List<PatrolArea> getChildList(List<PatrolArea> list, PatrolArea t) {
        List<PatrolArea> tlist = new ArrayList<PatrolArea>();
        Iterator<PatrolArea> it = list.iterator();
        while (it.hasNext())
        {
            PatrolArea n = (PatrolArea) it.next();
            if (StringUtils.isNotNull(n.getAreaPid()) && n.getAreaPid().longValue() == t.getAreaId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 查询巡检区域列表
     *
     * @param patrolArea 巡检区域
     * @return 巡检区域
     */
    @Override
    public List<PatrolArea> selectPatrolAreaList(PatrolAreaListReqVO patrolArea) {
        return patrolAreaMapper.selectPatrolAreaList(patrolArea);
    }

    /**
     * 新增巡检区域
     *
     * @param patrolArea 巡检区域
     * @return 结果
     */
    @Override
    public int insertPatrolArea(PatrolAreaSaveReqVO patrolAreaSaveReqVO) {
        if (patrolAreaSaveReqVO.getAreaPid() == null) {
            patrolAreaSaveReqVO.setAreaPid(PatrolArea.PARENT_ID_ROOT);
        }
        // 校验父区域的有效性
        validateParentArea(null, patrolAreaSaveReqVO.getAreaPid());
        // 校验区域名的唯一性
        validateAreaNameUnique(null, patrolAreaSaveReqVO.getAreaPid(), patrolAreaSaveReqVO.getAreaName());
        PatrolArea patrolArea = BeanUtils.toBean(patrolAreaSaveReqVO, PatrolArea.class);
        patrolArea.setCreateBy(SecurityUtils.getUsername());
        patrolArea.setCreateTime(DateUtils.getNowDate());
        //规定顶级区域的直接子区域的pid和ancestor均为0，所以如果下行代码传入selectById方法的pid为0，则查询结果parentArea为null，则需将新区域的ancestors设置为0
        //若新区域的pid不为0，则查询结果parentArea不会为null，则将pid代表的父区域的ancestors + "," + pid得到新区域的ancestors
        PatrolArea parentArea = patrolAreaMapper.selectById(patrolArea.getAreaPid());
        if (parentArea != null) {
            patrolArea.setAncestors(parentArea.getAncestors() + "," + patrolArea.getAreaPid());
        } else {
            patrolArea.setAncestors(patrolArea.getAreaPid().toString());
        }
        return patrolAreaMapper.insertPatrolArea(patrolArea);

    }

    /**
     * 修改巡检区域
     *
     * @param patrolArea 巡检区域
     * @return 结果
     */
    @Override
    public int updatePatrolArea(PatrolAreaSaveReqVO patrolAreaSaveReqVO) {
        PatrolArea oldArea = patrolAreaMapper.selectPatrolAreaByAreaId(patrolAreaSaveReqVO.getAreaId());
        PatrolArea patrolArea = BeanUtils.toBean(patrolAreaSaveReqVO, PatrolArea.class);
        if(oldArea == null)
            throw new ServiceException("当前区域不存在");
        if(StringUtils.isNotBlank(patrolAreaSaveReqVO.getAreaName())) {
            validateAreaNameUnique(patrolAreaSaveReqVO.getAreaId(), patrolAreaSaveReqVO.getAreaPid(), patrolAreaSaveReqVO.getAreaName());
        }
        if(patrolArea.getAreaPid() != null && !oldArea.getAreaPid().equals(patrolArea.getAreaPid())) { //当区域的pid发生变化时才维护ancestors，否则没必要再执行下面的代码进行维护，因为pid一样的话ancestors依然会保持不变
            validateParentArea(patrolAreaSaveReqVO.getAreaId(), patrolAreaSaveReqVO.getAreaPid());
            //进入该if的代码块则表示pid发生了变化
            PatrolArea newParentArea = patrolAreaMapper.selectPatrolAreaByAreaId(patrolArea.getAreaPid());  //查询新的pid对应的区域，若新pid=0，则查询出的newParentArea为空，否则非空
            String newAncestors = null;
            String oldAncestors = oldArea.getAncestors();   //记录旧的ancestors
            if(StringUtils.isNotNull(newParentArea))    //如果newParentArea非空，即新pid不为0
                newAncestors = newParentArea.getAncestors() + "," + newParentArea.getAreaId();  //则拼接新的父区域的ancestors + "," + 新的父区域的id得到本区域新的ancestors
            else    //如果newParentArea为空，即新pid为0，则直接将本区域新的ancestors设置为0
                newAncestors = patrolArea.getAreaPid().toString();
            patrolArea.setAncestors(newAncestors);
            updateAreaChildren(patrolArea.getAreaId(), newAncestors, oldAncestors); //由于本区域的ancestors发生变化，那么本区域的子区域的ancestors肯定跟着变化，都需要更新
        }

        patrolArea.setUpdateBy(SecurityUtils.getUsername());
        patrolArea.setUpdateTime(DateUtils.getNowDate());
        return patrolAreaMapper.updatePatrolArea(patrolArea);
    }

    public void updateAreaChildren(Long areaId, String newAncestors, String oldAncestors) {
        List<PatrolArea> children = patrolAreaMapper.selectChildrenAreaById(areaId);    //先查出本区域的所有子区域（包括直接子区域和间接子区域）
        //在理解下面的代码之前，明白一点很重要：假设A区域是B的父区域，B是C的父区域，那么B的ancestors一定以A的ancestors开头，C的ancestors一定以B的ancestors开头，那么C的ancestors也一定以A的ancestors开头
        //即若C是A的直接子区域或间接子区域（即A包含B，B包含C），那么C的ancestors一定以A的ancestors开头
        //对于开头的解释：有两字符串str1="1,2,3",str2="1,2"，则可以说str1以str2开头
        for (PatrolArea child : children) {
            //那么只需要将每个直接子区域或间接子区域的ancestors开头部分的oldAncestors（本区域的原ancestors）改成newAncestors即可
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            patrolAreaMapper.updateAreaChildren(children);
        }
    }

    /**
     * 批量删除巡检区域
     *
     * @param areaIds 需要删除的巡检区域主键
     * @return 结果
     */
    @Override
    public int deletePatrolAreaByAreaIds(Long[] areaIds) {
        Long count = patrolPointMapper.selectCount(new LambdaQueryWrapper<PatrolPoint>().eq(PatrolPoint::getDelFlag, "0").in(PatrolPoint::getAreaId, areaIds));
        if(count > 0) {
            throw new ServiceException("选择的区域中仍存在点位，不可删除");
        }
        return patrolAreaMapper.deletePatrolAreaByAreaIds(areaIds);
    }

    /**
     * 删除巡检区域信息
     *
     * @param areaId 巡检区域主键
     * @return 结果
     */
    @Override
    public int deletePatrolAreaByAreaId(Long areaId) {
        Long count = patrolPointMapper.selectCount(new LambdaQueryWrapper<PatrolPoint>().eq(PatrolPoint::getDelFlag, "0").eq(PatrolPoint::getAreaId, areaId));
        if(count > 0) {
            throw new ServiceException("选择的区域中仍存在点位，不可删除");
        }
        return patrolAreaMapper.deletePatrolAreaByAreaId(areaId);
    }

    private void validateParentArea(Long id, Long parentId) {
        if (parentId == null || PatrolArea.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为父区域
        if (Objects.equals(id, parentId)) {
            throw new ServiceException("不能设置自己为父区域");
        }
        // 2. 父区域不存在
        PatrolArea parentArea = patrolAreaMapper.selectById(parentId);
        if (parentArea == null) {
            throw new ServiceException("父区域不存在");
        }
        // 3. 递归校验父区域，如果父区域是自己的子区域，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentId = parentArea.getParentId();
            if (Objects.equals(id, parentId)) {
                throw new ServiceException("不能设置自己的子区域为父区域");
            }
            // 3.2 继续递归下一级父区域
            if (parentId == null || PatrolArea.PARENT_ID_ROOT.equals(parentId)) {
                break;
            }
            parentArea = patrolAreaMapper.selectById(parentId);
            if (parentArea == null) {
                break;
            }
        }
    }

    private void validateAreaNameUnique(Long id, Long parentId, String name) {
        PatrolArea area = patrolAreaMapper.selectOne(new LambdaQueryWrapper<PatrolArea>().eq(PatrolArea::getAreaPid, parentId).eq(PatrolArea::getAreaName, name).eq(PatrolArea::getDelFlag, "0"));
        if (area == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的区域
        if (id == null) {
            throw new ServiceException("已经存在该名字的区域");
        }
        if (ObjectUtil.notEqual(area.getAreaId(), id)) {
            throw new ServiceException("已经存在该名字的区域");
        }
    }
}
