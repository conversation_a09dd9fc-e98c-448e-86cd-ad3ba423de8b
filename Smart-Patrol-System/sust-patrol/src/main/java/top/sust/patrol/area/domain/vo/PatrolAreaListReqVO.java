package top.sust.patrol.area.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PatrolAreaListReqVO {
    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;

    @ApiModelProperty(value = "父区域ID", name = "areaPid")
    private Long areaPid;

    @ApiModelProperty(value = "区域名称", name = "areaName")
    private String areaName;

    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

}
