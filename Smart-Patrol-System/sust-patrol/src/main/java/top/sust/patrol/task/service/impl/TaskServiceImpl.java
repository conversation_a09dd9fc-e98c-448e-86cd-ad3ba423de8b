package top.sust.patrol.task.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
        import top.sust.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.sust.common.utils.SecurityUtils;
import top.sust.patrol.task.mapper.TaskMapper;
import top.sust.patrol.task.domain.Task;
import top.sust.patrol.task.service.ITaskService;

/**
 * 任务计划demoService业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, Task> implements ITaskService {
    @Autowired
    private TaskMapper taskMapper;

    /**
     * 查询任务计划demo
     *
     * @param taskId 任务计划demo主键
     * @return 任务计划demo
     */
    @Override
    public Task selectTaskByTaskId(Long taskId) {
        return taskMapper.selectTaskByTaskId(taskId);
    }

    /**
     * 查询任务计划demo列表
     *
     * @param task 任务计划demo
     * @return 任务计划demo
     */
    @Override
    public List<Task> selectTaskList(Task task) {
        return taskMapper.selectTaskList(task);
    }

    /**
     * 新增任务计划demo
     *
     * @param task 任务计划demo
     * @return 结果
     */
    @Override
    public int insertTask(Task task) {
        task.setCreateBy(SecurityUtils.getUsername());
        task.setCreateTime(DateUtils.getNowDate());
        return taskMapper.insertTask(task);
    }

    /**
     * 修改任务计划demo
     *
     * @param task 任务计划demo
     * @return 结果
     */
    @Override
    public int updateTask(Task task) {
        task.setCreateBy(SecurityUtils.getUsername());
        task.setUpdateTime(DateUtils.getNowDate());
        return taskMapper.updateTask(task);
    }

    /**
     * 批量删除任务计划demo
     *
     * @param taskIds 需要删除的任务计划demo主键
     * @return 结果
     */
    @Override
    public int deleteTaskByTaskIds(Long[] taskIds) {
        return taskMapper.deleteTaskByTaskIds(taskIds);
    }

    /**
     * 删除任务计划demo信息
     *
     * @param taskId 任务计划demo主键
     * @return 结果
     */
    @Override
    public int deleteTaskByTaskId(Long taskId) {
        return taskMapper.deleteTaskByTaskId(taskId);
    }
}
