package top.sust.patrol.route.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巡检路线简单响应VO
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "PatrolRouteSimpleRespVO", description = "巡检路线简单响应对象")
public class PatrolRouteSimpleRespVO {

    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;

    @ApiModelProperty(value = "路线名称", name = "routeName")
    private String routeName;

    @ApiModelProperty(value = "所属部门ID", name = "deptId")
    private Long deptId;

    @ApiModelProperty(value = "所属部门名称", name = "deptName")
    private String deptName;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;
}