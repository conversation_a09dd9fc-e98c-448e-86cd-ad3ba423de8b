package top.sust.patrol.team.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巡逻队伍简单响应VO
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@ApiModel(value = "PatrolTeamSimpleRespVO", description = "巡逻队伍简单响应VO")
public class PatrolTeamSimpleRespVO {

    @ApiModelProperty(value = "巡逻队ID", name = "teamId")
    private Long teamId;

    @ApiModelProperty(value = "巡逻队名称", name = "teamName")
    private String teamName;

    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;

    @ApiModelProperty(value = "部门名称", name = "deptName")
    private String deptName;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;
}
