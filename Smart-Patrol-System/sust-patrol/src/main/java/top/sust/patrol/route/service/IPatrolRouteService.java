package top.sust.patrol.route.service;

import java.util.List;
import top.sust.patrol.route.domain.PatrolRoute;
import top.sust.patrol.route.domain.vo.PatrolRouteSaveReqVO;
import top.sust.patrol.route.domain.vo.PatrolRouteListReqVO;
import top.sust.patrol.route.domain.vo.PatrolRouteSimpleRespVO;
import top.sust.patrol.route.domain.vo.PatrolRouteRespVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 巡检路线Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IPatrolRouteService extends IService<PatrolRoute> {
    /**
     * 查询巡检路线
     *
     * @param routeId 巡检路线主键
     * @return 巡检路线
     */
    public PatrolRouteRespVO selectPatrolRouteByRouteId(Long routeId);

    /**
     * 查询巡检路线列表
     *
     * @param patrolRoute 巡检路线
     * @return 巡检路线集合
     */
    public List<PatrolRoute> selectPatrolRouteList(PatrolRoute patrolRoute);

    /**
     * 查询巡检路线列表（包含部门名称和关联点位）
     *
     * @param listReqVO 巡检路线查询条件
     * @return 巡检路线响应VO集合
     */
    public List<PatrolRouteRespVO> listPatrolRoutesWithDetails(PatrolRouteListReqVO listReqVO);

    /**
     * 查询巡检路线简单列表
     *
     * @param listReqVO 巡检路线查询条件
     * @return 巡检路线简单响应VO集合
     */
    public List<PatrolRouteSimpleRespVO> listSimplePatrolRoutes(PatrolRouteListReqVO listReqVO);

    /**
     * 根据ID获取巡检路线详情（包含关联点位）
     *
     * @param routeId 路线ID
     * @return 巡检路线响应VO
     */
    public PatrolRouteRespVO getPatrolRouteWithDetails(Long routeId);

    /**
     * 新增巡检路线
     *
     * @param saveReqVO 巡检路线保存请求VO
     * @return 结果
     */
    public int savePatrolRoute(PatrolRouteSaveReqVO saveReqVO);

    /**
     * 修改巡检路线
     *
     * @param saveReqVO 巡检路线保存请求VO
     * @return 结果
     */
    public int updatePatrolRoute(PatrolRouteSaveReqVO saveReqVO);

    /**
     * 新增巡检路线（兼容原方法）
     *
     * @param patrolRoute 巡检路线
     * @return 结果
     */
    public int insertPatrolRoute(PatrolRoute patrolRoute);

    /**
     * 修改巡检路线（兼容原方法）
     *
     * @param patrolRoute 巡检路线
     * @return 结果
     */
    public int updatePatrolRoute(PatrolRoute patrolRoute);

    /**
     * 批量删除巡检路线
     *
     * @param routeIds 需要删除的巡检路线主键集合
     * @return 结果
     */
    public int deletePatrolRouteByRouteIds(Long[] routeIds);

    /**
     * 删除巡检路线信息
     *
     * @param routeId 巡检路线主键
     * @return 结果
     */
    public int deletePatrolRouteByRouteId(Long routeId);



    /**
     * 校验巡检路线名称是否唯一
     *
     * @param route 巡检路线信息
     * @return 结果
     */
    public boolean validateRouteNameUnique(PatrolRouteSaveReqVO route);
    /**
     * 校验巡检路线点位是否重复
     *
     * @param route 巡检路线信息
     * @return 校验结果，true表示无重复，false表示有重复
     */
    public boolean validateRoutePointsUnique(PatrolRouteSaveReqVO route);
}
