package top.sust.patrol.schedule.service;

import java.util.List;
import top.sust.patrol.schedule.domain.Schedule;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 计划表Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IScheduleService extends IService<Schedule> {
    /**
     * 查询计划表
     *
     * @param scheduleId 计划表主键
     * @return 计划表
     */
    public Schedule selectScheduleByScheduleId(Long scheduleId);

    /**
     * 查询计划表列表
     *
     * @param schedule 计划表
     * @return 计划表集合
     */
    public List<Schedule> selectScheduleList(Schedule schedule);

    /**
     * 新增计划表
     *
     * @param schedule 计划表
     * @return 结果
     */
    public int insertSchedule(Schedule schedule);

    /**
     * 修改计划表
     *
     * @param schedule 计划表
     * @return 结果
     */
    public int updateSchedule(Schedule schedule);

    /**
     * 批量删除计划表
     *
     * @param scheduleIds 需要删除的计划表主键集合
     * @return 结果
     */
    public int deleteScheduleByScheduleIds(Long[] scheduleIds);

    /**
     * 删除计划表信息
     *
     * @param scheduleId 计划表主键
     * @return 结果
     */
    public int deleteScheduleByScheduleId(Long scheduleId);
}
