package top.sust.patrol.template.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import top.sust.patrol.template.domain.PatrolItemTemplate;
import top.sust.patrol.template.domain.vo.PatrolItemTemplateListReqVO;

/**
 * 点位检查项模板Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IPatrolItemTemplateService extends IService<PatrolItemTemplate> {
    /**
     * 查询点位检查项模板
     *
     * @param templateId 点位检查项模板主键
     * @return 点位检查项模板
     */
    public PatrolItemTemplate selectPatrolItemTemplateByTemplateId(Long templateId);

    /**
     * 查询点位检查项模板列表
     *
     * @param patrolItemTemplate 点位检查项模板
     * @return 点位检查项模板集合
     */
    public List<PatrolItemTemplate> selectPatrolItemTemplateList(PatrolItemTemplateListReqVO patrolItemTemplate);

    /**
     * 新增点位检查项模板
     *
     * @param patrolItemTemplate 点位检查项模板
     * @return 结果
     */
    public int insertPatrolItemTemplate(PatrolItemTemplate patrolItemTemplate);

    /**
     * 修改点位检查项模板
     *
     * @param patrolItemTemplate 点位检查项模板
     * @return 结果
     */
    public int updatePatrolItemTemplate(PatrolItemTemplate patrolItemTemplate);

    /**
     * 批量删除点位检查项模板
     *
     * @param templateIds 需要删除的点位检查项模板主键集合
     * @return 结果
     */
    public int deletePatrolItemTemplateByTemplateIds(Long[] templateIds);

    /**
     * 删除点位检查项模板信息
     *
     * @param templateId 点位检查项模板主键
     * @return 结果
     */
    public int deletePatrolItemTemplateByTemplateId(Long templateId);
}
