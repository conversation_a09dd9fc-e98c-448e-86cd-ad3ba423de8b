package top.sust.patrol.route.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 巡检路线保存请求VO
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "PatrolRouteSaveReqVO", description = "巡检路线保存请求对象")
public class PatrolRouteSaveReqVO {

    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;

    @NotBlank(message = "路线名称不能为空")
    @ApiModelProperty(value = "路线名称", name = "routeName", required = true)
    private String routeName;

    @NotNull(message = "所属部门不能为空")
    @ApiModelProperty(value = "所属部门ID", name = "deptId", required = true)
    private Long deptId;

    @NotBlank(message = "状态不能为空")
    @ApiModelProperty(value = "状态", name = "status", required = true)
    private String status;

    @ApiModelProperty(value = "关联的点位ID列表", name = "pointIds")
    private List<Long> pointIds;

    @ApiModelProperty(value = "关联表中的点位详细信息", name = "routePoints")
    private List<RoutePointInfo> routePoints;

    /**
     * 路线点位信息内部类（用于修改时设置点位顺序和状态）
     */
    @Data
    @ApiModel(value = "RoutePointInfo", description = "路线点位信息")
    public static class RoutePointInfo {
        @ApiModelProperty(value = "点位ID", name = "pointId", required = true)
        private Long pointId;

        @ApiModelProperty(value = "点位在路线中的顺序", name = "pointOrder", required = true)
        private Integer pointOrder;

        @ApiModelProperty(value = "点位状态", name = "status", required = true)
        private String status;
    }
}