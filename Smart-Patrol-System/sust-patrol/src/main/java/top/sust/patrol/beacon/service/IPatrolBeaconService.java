package top.sust.patrol.beacon.service;

import java.util.List;

import top.sust.common.core.domain.entity.SysUser;
import top.sust.patrol.beacon.domain.PatrolBeacon;
import com.baomidou.mybatisplus.extension.service.IService;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconListReqVO;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconSaveReqVO;

/**
 * 信标管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IPatrolBeaconService extends IService<PatrolBeacon> {
    /**
     * 查询信标管理
     *
     * @param beaconId 信标管理主键
     * @return 信标管理
     */
    public PatrolBeacon selectPatrolBeaconByBeaconId(Long beaconId);

    /**
     * 查询信标管理列表
     *
     * @param patrolBeacon 信标管理
     * @return 信标管理集合
     */
    public List<PatrolBeacon> selectPatrolBeaconList(PatrolBeaconListReqVO patrolBeacon);

    /**
     * 新增信标管理
     *
     * @param patrolBeacon 信标管理
     * @return 结果
     */
    public int insertPatrolBeacon(PatrolBeaconSaveReqVO patrolBeacon);

    /**
     * 修改信标管理
     *
     * @param patrolBeacon 信标管理
     * @return 结果
     */
    public int updatePatrolBeacon(PatrolBeaconSaveReqVO patrolBeacon);

    /**
     * 批量删除信标管理
     *
     * @param beaconIds 需要删除的信标管理主键集合
     * @return 结果
     */
    public int deletePatrolBeaconByBeaconIds(Long[] beaconIds);

    /**
     * 删除信标管理信息
     *
     * @param beaconId 信标管理主键
     * @return 结果
     */
    public int deletePatrolBeaconByBeaconId(Long beaconId);

    /**
     * 导入信标信息
     */
    public String importBeacon(List<PatrolBeacon> beaconList, Boolean isUpdateSupport, String operName);
}
