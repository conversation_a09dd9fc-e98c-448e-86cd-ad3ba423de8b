package top.sust.patrol.schedule.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import top.sust.common.core.domain.BaseEntity;
import java.time.LocalDateTime;
import java.time.LocalTime;
/**
 * 计划表对象 patrol_schedule
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.schedule.mapper.ScheduleMapper.ScheduleResult")
@ApiModel(value = "Schedule", description = "计划表对象")
public class Schedule extends BaseEntity{
private static final long serialVersionUID=1L;
    /** 计划ID */
    @TableId(value = "schedule_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "${comment}", name = "scheduleId")
    private Long scheduleId;
    /** 路线ID */
    @Excel(name = "路线ID" )
    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;
    /** 计划名称 */
    @Excel(name = "计划名称" )
    @ApiModelProperty(value = "计划名称", name = "scheduleName")
    private String scheduleName;
    /** 队伍ID */
    @Excel(name = "队伍ID" )
    @ApiModelProperty(value = "队伍ID", name = "teamId")
    private Long teamId;
    /** 每日开始时间 */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "每日开始时间" , width = 30, dateFormat = "HH:mm:ss" )
    @ApiModelProperty(value = "每日开始时间", name = "scheduledStartTime")
    private LocalTime scheduledStartTime;
    /** 时长（单位：分钟） */
    @Excel(name = "时长" , readConverterExp = "单=位：分钟" )
    @ApiModelProperty(value = "时长", name = "duration")
    private Long duration;
    /** 周期（指定一周内哪几天执行，例如1,2,3,4,5） */
    @Excel(name = "周期" , readConverterExp = "指=定一周内哪几天执行，例如1,2,3,4,5" )
    @ApiModelProperty(value = "周期", name = "period")
    private String period;
    /** 状态 */
    @Excel(name = "状态" )
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /** 逻辑删除 */
    @ApiModelProperty(value = "状态", name = "delFlag")
    private String delFlag;
}
