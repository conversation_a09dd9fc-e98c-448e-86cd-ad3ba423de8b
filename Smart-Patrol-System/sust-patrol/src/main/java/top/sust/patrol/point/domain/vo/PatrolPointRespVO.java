package top.sust.patrol.point.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import top.sust.patrol.point.domain.PatrolPointItem;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PatrolPointRespVO {
    
    @ApiModelProperty(value = "点位ID", name = "pointId")
    private Long pointId;

    @ApiModelProperty(value = "点位名称", name = "pointName")
    private String pointName;

    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;

    @ApiModelProperty(value = "信标ID", name = "beaconId")
    private Long beaconId;

    @ApiModelProperty(value = "信标名称", name = "beaconName")
    private String beaconName;

    @ApiModelProperty(value = "经度", name = "longtitude")
    private BigDecimal longtitude;
 
    @ApiModelProperty(value = "纬度", name = "latitude")
    private BigDecimal latitude;
 
    @ApiModelProperty(value = "X坐标", name = "locationX")
    private BigDecimal locationX;
 
    @ApiModelProperty(value = "Y坐标", name = "locationY")
    private BigDecimal locationY;
 
    @ApiModelProperty(value = "图层", name = "locationLayer")
    private Long locationLayer;
 
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
   
    @ApiModelProperty(value = "巡检点位项目列表", name = "patrolPointItemList")
    private List<PatrolPointItem> patrolPointItemList;
}
