package top.sust.patrol.task.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import top.sust.common.core.domain.BaseEntity;

/**
 * 任务计划demo对象 patrol_task
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.task.mapper.TaskMapper.TaskResult")
@ApiModel(value = "Task", description = "任务计划demo对象")
public class Task extends BaseEntity{
private static final long serialVersionUID=1L;
    /** 任务ID */
    @TableId(value = "task_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "${comment}", name = "taskId")
    private Long taskId;
    /** 计划ID */
    @Excel(name = "计划ID" )
    @ApiModelProperty(value = "计划ID", name = "scheduleId")
    private Long scheduleId;
    /** 路线ID */
    @Excel(name = "路线ID" )
    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;
    /** 任务名称 */
    @Excel(name = "任务名称" )
    @ApiModelProperty(value = "任务名称", name = "taskName")
    private String taskName;
    /** 任务类型 */
    @Excel(name = "任务类型" )
    @ApiModelProperty(value = "任务类型", name = "taskType")
    private String taskType;
    /** 队伍ID */
    @Excel(name = "队伍ID" )
    @ApiModelProperty(value = "队伍ID", name = "teamId")
    private Long teamId;
    /** 计划巡逻队用户 */
    @Excel(name = "计划巡逻队用户" )
    @ApiModelProperty(value = "计划巡逻队用户", name = "scheduledPatrolUsers")
    private String scheduledPatrolUsers;
    /** 计划任务时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "计划任务时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @ApiModelProperty(value = "计划任务时间", name = "scheduledStartTime")
    private Date scheduledStartTime;
    /** 计划任务时长(单位：分钟) */
    @Excel(name = "计划任务时长(单位：分钟)" )
    @ApiModelProperty(value = "计划任务时长(单位：分钟)", name = "duration")
    private Long duration;
    /** 实际完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "实际完成时间" , width = 30, dateFormat = "yyyy-MM-dd" )
    @ApiModelProperty(value = "实际完成时间", name = "actualCompleteTime")
    private Date actualCompleteTime;
    /** 状态（未完成0、完成1、因某种原因取消2） */
    @Excel(name = "状态" , readConverterExp = "未=完成0、完成1、因某种原因取消2" )
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /** 是否正常（0正常，1异常） */
    @Excel(name = "是否正常" , readConverterExp = "0=正常，1异常" )
    @ApiModelProperty(value = "是否正常", name = "normalStatus")
    private String normalStatus;
    /** 逻辑删除 */
    @ApiModelProperty(value = "是否正常", name = "delFlag")
    private String delFlag;
}
