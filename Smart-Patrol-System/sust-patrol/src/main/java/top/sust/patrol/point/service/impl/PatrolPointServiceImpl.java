package top.sust.patrol.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.sust.common.utils.DateUtils;
import top.sust.common.utils.SecurityUtils;
import top.sust.common.exception.ServiceException;

import java.util.ArrayList;
import java.util.List;
import top.sust.common.utils.StringUtils;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.patrol.point.domain.PatrolPointItem;
import top.sust.patrol.point.domain.vo.PatrolPointListReqVO;
import top.sust.patrol.point.domain.vo.PatrolPointSaveReqVO;
import top.sust.patrol.point.mapper.PatrolPointMapper;
import top.sust.patrol.point.domain.PatrolPoint;
import top.sust.patrol.point.service.IPatrolPointService;

/**
 * 巡检点位Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class PatrolPointServiceImpl extends ServiceImpl<PatrolPointMapper, PatrolPoint> implements IPatrolPointService {
    @Autowired
    private PatrolPointMapper patrolPointMapper;

    /**
     * 查询巡检点位
     *
     * @param pointId 巡检点位主键
     * @return 巡检点位
     */
    @Override
    public PatrolPoint selectPatrolPointByPointId(Long pointId) {
        return patrolPointMapper.selectPatrolPointByPointId(pointId);
    }

    /**
     * 查询巡检点位列表
     *
     * @param patrolPoint 巡检点位查询条件
     * @return 巡检点位
     */
    @Override
    public List<PatrolPoint> selectPatrolPointList(PatrolPointListReqVO patrolPoint) {
        return patrolPointMapper.selectPatrolPointList(patrolPoint);
    }

    /**
     * 检查信标ID是否已被其他点位使用
     *
     * @param beaconId 信标ID
     * @param pointId 当前点位ID（更新时使用，新增时为null）
     * @return 是否已被使用
     */
    private boolean isBeaconIdUsedByOtherPoint(Long beaconId, Long pointId) {
        // 查询数据库中该信标ID是否被使用
        List<PatrolPoint> existingPoints = patrolPointMapper.selectPatrolPointByBeaconId(beaconId);
        if (existingPoints == null || existingPoints.isEmpty()) {
            return false;
        }
        // 如果是更新操作，排除当前点位
        if (pointId != null) {
            return existingPoints.stream().anyMatch(point -> !pointId.equals(point.getPointId()));
        }
        // 如果是新增操作，只要存在就说明被使用了
        return true;
    }

    /**
     * 检查相同区域下是否存在相同名称的点位
     *
     * @param pointName 点位名称
     * @param areaId 区域ID
     * @param pointId 当前点位ID（更新时使用，新增时为null）
     * @return 是否存在
     */
    private boolean isPointNameExistsInArea(String pointName, Long areaId, Long pointId) {
        //创建PatrolPointListReqVO来实现查询
        PatrolPointListReqVO queryVO = new PatrolPointListReqVO();
        queryVO.setPointName(pointName);
        queryVO.setAreaId(areaId);
        //查询数据库中是否存在该点位名称
        List<PatrolPoint> existingPoints = patrolPointMapper.selectPatrolPointList(queryVO);
        if (existingPoints == null || existingPoints.isEmpty()) {
            return false;
        }
        // 如果是更新操作，排除当前点位
        if (pointId != null) {
            return existingPoints.stream().anyMatch(point -> !pointId.equals(point.getPointId()));
        }
        // 如果是新增操作，只要存在就说明重复了
        return true;
    }

    /**
     * 新增巡检点位
     *
     * @param patrolPointSaveReqVO 巡检点位保存请求
     * @return 结果
     */
    @Transactional
    @Override
    public int insertPatrolPoint(PatrolPointSaveReqVO patrolPointSaveReqVO) {
        // 检查信标ID是否已被其他点位使用
        if (isBeaconIdUsedByOtherPoint(patrolPointSaveReqVO.getBeaconId(), null)) {
            throw new ServiceException("信标ID已被其他点位使用，请选择其他信标");
        }
        // 检查相同区域下是否存在相同名称的点位
        if (isPointNameExistsInArea(patrolPointSaveReqVO.getPointName(), patrolPointSaveReqVO.getAreaId(), null)) {
            throw new ServiceException("该区域下已存在相同名称的点位，请使用其他名称");
        }
        PatrolPoint patrolPoint = BeanUtils.toBean(patrolPointSaveReqVO, PatrolPoint.class);
        patrolPoint.setCreateBy(SecurityUtils.getUsername());
        patrolPoint.setCreateTime(DateUtils.getNowDate());
        int rows = patrolPointMapper.insertPatrolPoint(patrolPoint);
        insertPatrolPointItem(patrolPoint);
        return rows;
    }

    /**
     * 修改巡检点位
     *
     * @param patrolPointSaveReqVO 巡检点位保存请求
     * @return 结果
     */
    @Transactional
    @Override
    public int updatePatrolPoint(PatrolPointSaveReqVO patrolPointSaveReqVO) {
        // 检查信标ID是否已被其他点位使用
        if (isBeaconIdUsedByOtherPoint(patrolPointSaveReqVO.getBeaconId(), patrolPointSaveReqVO.getPointId())) {
            throw new ServiceException("信标ID已被其他点位使用，请选择其他信标");
        }
        // 检查相同区域下是否存在相同名称的点位
        if (isPointNameExistsInArea(patrolPointSaveReqVO.getPointName(), patrolPointSaveReqVO.getAreaId(), patrolPointSaveReqVO.getPointId())) {
            throw new ServiceException("该区域下已存在相同名称的点位，请使用其他名称");
        }
        PatrolPoint patrolPoint = BeanUtils.toBean(patrolPointSaveReqVO, PatrolPoint.class);
        patrolPoint.setUpdateBy(SecurityUtils.getUsername());
        patrolPoint.setUpdateTime(DateUtils.getNowDate());
        patrolPointMapper.deletePatrolPointItemByPointId(patrolPoint.getPointId());
        insertPatrolPointItem(patrolPoint);
        return patrolPointMapper.updatePatrolPoint(patrolPoint);
    }

    /**
     * 批量删除巡检点位
     *
     * @param pointIds 需要删除的巡检点位主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deletePatrolPointByPointIds(Long[] pointIds) {
        // 检查点位是否被巡检路线使用
        int routeCount = countRoutesUsingPoints(pointIds);
        if (routeCount > 0) {
            throw new ServiceException("选中的点位正在被巡检路线使用，无法删除。请先从巡检路线中移除该点位后再删除。");
        }
        patrolPointMapper.deletePatrolPointItemByPointIds(pointIds);
        return patrolPointMapper.deletePatrolPointByPointIds(pointIds);
    }

    /**
     * 检查点位是否被巡检路线使用
     *
     * @param pointIds 点位ID数组
     * @return 使用该点位的路线数量
     */
    @Override
    public int countRoutesUsingPoints(Long[] pointIds) {
        return patrolPointMapper.countRoutesUsingPoints(pointIds);
    }

    /**
     * 新增巡检点位项目信息
     *
     * @param patrolPoint 巡检点位对象
     */
    public void insertPatrolPointItem(PatrolPoint patrolPoint) {
        List<PatrolPointItem> patrolPointItemList = patrolPoint.getPatrolPointItemList();
        Long pointId = patrolPoint.getPointId();
        if (StringUtils.isNotNull(patrolPointItemList))
        {
            List<PatrolPointItem> list = new ArrayList<PatrolPointItem>();
            for (PatrolPointItem patrolPointItem : patrolPointItemList)
            {
                patrolPointItem.setPointId(pointId);
                // 确保基础字段不为空
                String createBy = patrolPoint.getCreateBy();
                java.util.Date createTime = patrolPoint.getCreateTime();
                String updateBy = patrolPoint.getUpdateBy();
                java.util.Date updateTime = patrolPoint.getUpdateTime();
                // 如果createBy为空，使用当前用户
                if (StringUtils.isEmpty(createBy)) {
                    createBy = SecurityUtils.getUsername();
                }
                if (createTime == null) {
                    createTime = DateUtils.getNowDate();
                }
                // 更新操作
                if (updateBy != null) {
                    patrolPointItem.setUpdateBy(updateBy);
                    patrolPointItem.setUpdateTime(updateTime);
                    // 更新操作时，只为新的PatrolPointItem设置创建信息
                    if (patrolPointItem.getItemId() == null || StringUtils.isEmpty(patrolPointItem.getCreateBy())) {
                        patrolPointItem.setCreateBy(createBy);
                        patrolPointItem.setCreateTime(createTime);
                    }
                } else {
                    // 新增操作
                    patrolPointItem.setCreateBy(createBy);
                    patrolPointItem.setCreateTime(createTime);
                }
                // 设置删除标志默认值
                if (StringUtils.isEmpty(patrolPointItem.getDelFlag())) {
                    patrolPointItem.setDelFlag("0");
                }
                list.add(patrolPointItem);
            }
            if (list.size() > 0)
            {
                patrolPointMapper.batchPatrolPointItem(list);
            }
        }
    }
}
