package top.sust.patrol.task.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.enums.BusinessType;
import top.sust.patrol.task.domain.Task;
import top.sust.patrol.task.service.ITaskService;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.common.core.page.TableDataInfo;

/**
 * 任务计划demo接口
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Api(tags = "任务计划demo管理接口")
@RestController
@RequestMapping("/task/task")
public class TaskController extends BaseController
{
    @Autowired
    private ITaskService taskService;

    /**
     * 查询任务计划demo列表
     */
    @ApiOperation(value = "查询任务计划demo列表")
    @PreAuthorize("@ss.hasPermi('task:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(Task task)
    {
        startPage();
        List<Task> list = taskService.selectTaskList(task);
        return getDataTable(list);
    }

    /**
     * 导出任务计划demo列表
     */
    @ApiOperation(value = "导出任务计划demo列表")
    @PreAuthorize("@ss.hasPermi('task:task:export')")
    @Log(title = "任务计划demo", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Task task)
    {
        List<Task> list = taskService.selectTaskList(task);
        ExcelUtil<Task> util = new ExcelUtil<Task>(Task.class);
        util.exportExcel(response, list, "任务计划demo数据");
    }

    /**
     * 获取任务计划demo详细信息
     */
    @ApiOperation(value = "获取任务计划demo详细信息")
    @PreAuthorize("@ss.hasPermi('task:task:query')")
    @GetMapping(value = "/{taskId}")
    public R<Task> getInfo(@PathVariable("taskId") Long taskId)
    {
        return R.ok(taskService.selectTaskByTaskId(taskId));
    }

    /**
     * 新增任务计划demo
     */
    @ApiOperation(value = "新增任务计划demo")
    @PreAuthorize("@ss.hasPermi('task:task:add')")
    @Log(title = "任务计划demo", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Task task)
    {
        return toAjax(taskService.insertTask(task));
    }

    /**
     * 修改任务计划demo
     */
    @ApiOperation(value = "修改任务计划demo")
    @PreAuthorize("@ss.hasPermi('task:task:edit')")
    @Log(title = "任务计划demo", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Task task)
    {
        return toAjax(taskService.updateTask(task));
    }

    /**
     * 删除任务计划demo
     */
    @ApiOperation(value = "删除任务计划demo")
    @PreAuthorize("@ss.hasPermi('task:task:remove')")
    @Log(title = "任务计划demo", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable Long[] taskIds)
    {
        return toAjax(taskService.deleteTaskByTaskIds(taskIds));
    }
}
