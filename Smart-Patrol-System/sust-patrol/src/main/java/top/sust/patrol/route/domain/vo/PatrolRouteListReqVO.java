package top.sust.patrol.route.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检路线查询请求VO
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "PatrolRouteListReqVO", description = "巡检路线查询请求对象")
public class PatrolRouteListReqVO {

    @ApiModelProperty(value = "路线名称，模糊匹配", name = "routeName")
    private String routeName;

    @ApiModelProperty(value = "所属部门ID", name = "deptId")
    private Long deptId;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;


}