package top.sust.patrol.team.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import top.sust.common.core.domain.entity.SysUser;

import java.util.List;

/**
 * 巡逻队伍详情响应VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ApiModel(value = "PatrolTeamDetailRespVO", description = "巡逻队伍详情响应VO")
public class PatrolTeamDetailRespVO {

    /** 巡逻队ID */
    @ApiModelProperty(value = "巡逻队ID", name = "teamId")
    private Long teamId;

    /** 巡逻队名称 */
    @ApiModelProperty(value = "巡逻队名称", name = "teamName")
    private String teamName;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;

    /** 部门名称 */
    @ApiModelProperty(value = "部门名称", name = "deptName")
    private String deptName;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    /** 队伍成员用户ID列表 */
    @ApiModelProperty(value = "队伍成员用户ID列表", name = "memberIds")
    private Long[] memberIds;

    /** 队伍成员列表 */
    @ApiModelProperty(value = "队伍成员列表", name = "members")
    private List<SysUser> members;
}
