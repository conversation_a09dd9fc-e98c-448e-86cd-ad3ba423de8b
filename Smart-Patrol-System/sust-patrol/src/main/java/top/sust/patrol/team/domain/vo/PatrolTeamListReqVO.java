package top.sust.patrol.team.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import top.sust.common.core.domain.BaseEntity;

/**
 * 巡逻队伍列表查询请求VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ApiModel(value = "PatrolTeamListReqVO", description = "巡逻队伍列表查询请求VO")
public class PatrolTeamListReqVO extends BaseEntity {

    /** 巡逻队名称 */
    @ApiModelProperty(value = "巡逻队名称", name = "teamName")
    private String teamName;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
}
