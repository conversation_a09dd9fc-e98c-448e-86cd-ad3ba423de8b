package top.sust.patrol.beacon.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "巡逻信标列表查询请求参数")
public class PatrolBeaconListReqVO {

    @ApiModelProperty(
            value = "信标逻辑名称（支持模糊查询）",
            example = "北门信标",
            notes = "根据信标的逻辑名称进行模糊搜索"
    )
    private String beaconName;

    @ApiModelProperty(
            value = "信标MAC地址（精确匹配）",
            example = "AA:BB:CC:11:22:33",
            notes = "根据信标的物理MAC地址精确查询"
    )
    private String beaconMac;

    @ApiModelProperty(
            value = "信标状态（启用/禁用）",
            example = "1",
            allowableValues = "0,1",
            notes = "0表示禁用，1表示启用"
    )
    private String status;

}