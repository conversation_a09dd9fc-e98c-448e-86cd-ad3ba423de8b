package top.sust.patrol.team.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import top.sust.common.annotation.Excel;
import top.sust.common.core.domain.BaseEntity;

/**
 * 巡逻队伍列表响应VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ApiModel(value = "PatrolTeamListRespVO", description = "巡逻队伍列表响应VO")
public class PatrolTeamListRespVO extends BaseEntity {

    /** 巡逻队ID */
    @Excel(name = "巡逻队ID")
    @ApiModelProperty(value = "巡逻队ID", name = "teamId")
    private Long teamId;

    /** 巡逻队名称 */
    @Excel(name = "巡逻队名称")
    @ApiModelProperty(value = "巡逻队名称", name = "teamName")
    private String teamName;

    /** 部门名称 */
    @Excel(name = "部门名称")
    @ApiModelProperty(value = "部门名称", name = "deptName")
    private String deptName;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    /** 成员数量 */
    @Excel(name = "成员数量")
    @ApiModelProperty(value = "成员数量", name = "memberCount")
    private Integer memberCount;

}
