package top.sust.patrol.beacon.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import top.sust.common.core.domain.entity.SysUser;
import top.sust.common.exception.ServiceException;
import top.sust.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.sust.common.utils.SecurityUtils;
import top.sust.common.utils.StringUtils;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.common.utils.bean.BeanValidators;
import top.sust.patrol.area.domain.PatrolArea;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconListReqVO;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconSaveReqVO;
import top.sust.patrol.beacon.mapper.PatrolBeaconMapper;
import top.sust.patrol.beacon.domain.PatrolBeacon;
import top.sust.patrol.beacon.service.IPatrolBeaconService;
import top.sust.patrol.point.domain.PatrolPoint;
import top.sust.patrol.point.domain.vo.PatrolPointListReqVO;
import top.sust.patrol.point.mapper.PatrolPointMapper;
import top.sust.patrol.point.service.IPatrolPointService;

import javax.validation.Validator;

/**
 * 信标管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class PatrolBeaconServiceImpl extends ServiceImpl<PatrolBeaconMapper, PatrolBeacon> implements IPatrolBeaconService {

    @Autowired
    private PatrolBeaconMapper patrolBeaconMapper;

    @Autowired
    protected Validator validator;

    @Autowired
    private PatrolPointMapper patrolPointMapper;

    @Autowired
    private IPatrolPointService patrolPointService;

    /**
     * 查询信标管理
     *
     * @param beaconId 信标管理主键
     * @return 信标管理
     */
    @Override
    public PatrolBeacon selectPatrolBeaconByBeaconId(Long beaconId) {
        return patrolBeaconMapper.selectPatrolBeaconByBeaconId(beaconId);
    }

    /**
     * 查询信标管理列表
     *
     * @param patrolBeacon 信标管理
     * @return 信标管理
     */
    @Override
    public List<PatrolBeacon> selectPatrolBeaconList(PatrolBeaconListReqVO patrolBeacon) {
        return patrolBeaconMapper.selectPatrolBeaconList(patrolBeacon);
    }

    /**
     * 新增信标管理
     *
     * @param patrolBeaconSaveReqVO 信标管理
     * @return 结果
     */
    @Override
    public int insertPatrolBeacon(PatrolBeaconSaveReqVO patrolBeaconSaveReqVO) {
        // 校验编号是否重复
        PatrolBeaconListReqVO checkReq = new PatrolBeaconListReqVO();
        checkReq.setBeaconName(patrolBeaconSaveReqVO.getBeaconName());
        List<PatrolBeacon> existingBeacons = patrolBeaconMapper.selectPatrolBeaconList(checkReq);
        if (existingBeacons != null && !existingBeacons.isEmpty()) {
            throw new RuntimeException("该编号已存在");
        }


        // 安全设置默认值 - 解决 'status' 字段无默认值问题
        if (patrolBeaconSaveReqVO.getStatus() == null) {
            patrolBeaconSaveReqVO.setStatus("0"); // 设置默认状态为 "0"（正常）
        }
        PatrolBeacon patrolBeacon = BeanUtils.toBean(patrolBeaconSaveReqVO, PatrolBeacon.class);
        patrolBeacon.setCreateBy(SecurityUtils.getUsername());
        patrolBeacon.setCreateTime(DateUtils.getNowDate());
        return patrolBeaconMapper.insertPatrolBeacon(patrolBeacon);
    }

    /**
     * 修改信标管理
     *
     * @param patrolBeaconSaveReqVO 信标管理
     * @return 结果
     */
    @Override
    public int updatePatrolBeacon(PatrolBeaconSaveReqVO patrolBeaconSaveReqVO) {

        // 检查信标编号是否重复（排除当前记录）
        PatrolBeaconListReqVO checkReq = new PatrolBeaconListReqVO();
        checkReq.setBeaconName(patrolBeaconSaveReqVO.getBeaconName());
        List<PatrolBeacon> existingBeacons = patrolBeaconMapper.selectPatrolBeaconList(checkReq);
        // 如果尝试修改编号且新编号已被占用
        if (existingBeacons != null) {
            for (PatrolBeacon beacon : existingBeacons) {
                if (!beacon.getBeaconId().equals(patrolBeaconSaveReqVO.getBeaconId())) {
                    throw new RuntimeException("该编号已存在");
                }
            }
        }

        PatrolBeacon patrolBeacon = BeanUtils.toBean(patrolBeaconSaveReqVO, PatrolBeacon.class);
        patrolBeacon.setUpdateBy(SecurityUtils.getUsername());
        patrolBeacon.setUpdateTime(DateUtils.getNowDate());
        return patrolBeaconMapper.updatePatrolBeacon(patrolBeacon);
    }

    /**
     * 批量删除信标管理
     *
     * @param beaconIds 需要删除的信标管理主键
     * @return 结果
     */
    @Override
    public int deletePatrolBeaconByBeaconIds(Long[] beaconIds) {
        // 检查信标是否被点位使用
        List<Long> usedBeacons = new ArrayList<>();
        for (Long beaconId : beaconIds) {
            PatrolPoint patrolPoint = patrolPointService.lambdaQuery().eq(PatrolPoint::getBeaconId, beaconId).one();
            if (patrolPoint != null){
                usedBeacons.add(beaconId);
            }
        }
        // 如果有被使用的信标，抛出异常
        if (!usedBeacons.isEmpty()) {
            String usedIds = usedBeacons.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            throw new ServiceException("信标编号为 [" + usedIds + "] 已被使用，不能删除");
        }


        return patrolBeaconMapper.deletePatrolBeaconByBeaconIds(beaconIds);
    }

    /**
     * 删除信标管理信息
     *
     * @param beaconId 信标管理主键
     * @return 结果
     */
    @Override
    public int deletePatrolBeaconByBeaconId(Long beaconId) {
        // 检查信标是否被点位使用
        PatrolPoint patrolPoint = patrolPointService.lambdaQuery().eq(PatrolPoint::getBeaconId, beaconId).one();
        if (patrolPoint != null){
            throw new ServiceException("信标编号为 " + beaconId + " 已被使用，不能删除");
        }

        return patrolBeaconMapper.deletePatrolBeaconByBeaconId(beaconId);
    }

    /**
     * 导入信标管理信息
     */
    @Override
    public String importBeacon(List<PatrolBeacon> beaconList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(beaconList) || beaconList.size() == 0)
        {
            throw new ServiceException("导入信标数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (PatrolBeacon beacon : beaconList)
        {
            try
            {
                // 验证是否存在这个信标
                PatrolBeacon patrolBeacon = lambdaQuery().eq(PatrolBeacon::getBeaconName, beacon.getBeaconName()).one();
                if (StringUtils.isNull(patrolBeacon))
                {
                    BeanValidators.validateWithException(validator, beacon);
                    beacon.setCreateTime(DateUtils.getNowDate());
                    beacon.setCreateBy(operName);
                    beacon.setDelFlag("0");
                    patrolBeaconMapper.insertPatrolBeacon(beacon);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、编号 " + beacon.getBeaconName() + " 导入成功");
                } else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、编号 " + beacon.getBeaconName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + beacon.getBeaconName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
