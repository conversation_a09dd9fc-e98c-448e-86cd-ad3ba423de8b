package top.sust.patrol.route.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import top.sust.common.annotation.DataScope;
import top.sust.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import top.sust.common.utils.SecurityUtils;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.patrol.route.mapper.PatrolRouteMapper;
import top.sust.patrol.route.domain.PatrolRoute;
import top.sust.patrol.route.domain.vo.PatrolRouteSaveReqVO;
import top.sust.patrol.route.domain.vo.PatrolRouteListReqVO;
import top.sust.patrol.route.domain.vo.PatrolRouteSimpleRespVO;
import top.sust.patrol.route.domain.vo.PatrolRouteRespVO;
import top.sust.patrol.route.service.IPatrolRouteService;

/**
 * 巡检路线Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class PatrolRouteServiceImpl extends ServiceImpl<PatrolRouteMapper, PatrolRoute> implements IPatrolRouteService {
    @Autowired
    private PatrolRouteMapper patrolRouteMapper;

    /**
     * 查询巡检路线
     *
     * @param routeId 巡检路线主键
     * @return 巡检路线
     */
    @Override
    public PatrolRouteRespVO selectPatrolRouteByRouteId(Long routeId) {
        return patrolRouteMapper.selectPatrolRouteByRouteId(routeId);
    }

    /**
     * 查询巡检路线列表
     *
     * @param patrolRoute 巡检路线
     * @return 巡检路线
     */
    @Override
    public List<PatrolRoute> selectPatrolRouteList(PatrolRoute patrolRoute) {
        return patrolRouteMapper.selectPatrolRouteList(patrolRoute);
    }

    /**
     * 新增巡检路线
     *
     * @param patrolRoute 巡检路线
     * @return 结果
     */
    @Override
    public int insertPatrolRoute(PatrolRoute patrolRoute) {
        patrolRoute.setCreateBy(SecurityUtils.getUsername());
        patrolRoute.setCreateTime(DateUtils.getNowDate());
        return patrolRouteMapper.insertPatrolRoute(patrolRoute);
    }

    /**
     * 修改巡检路线
     *
     * @param patrolRoute 巡检路线
     * @return 结果
     */
    @Override
    public int updatePatrolRoute(PatrolRoute patrolRoute) {
        patrolRoute.setCreateBy(SecurityUtils.getUsername());
        patrolRoute.setUpdateTime(DateUtils.getNowDate());
        return patrolRouteMapper.updatePatrolRoute(patrolRoute);
    }

    /**
     * 批量删除巡检路线
     *
     * @param routeIds 需要删除的巡检路线主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePatrolRouteByRouteIds(Long[] routeIds) {
        // 先删除所有关联的点位关系
        for (Long routeId : routeIds) {
            patrolRouteMapper.deleteRoutePointsByRouteId(routeId);
        }
        // 再删除路线
        return patrolRouteMapper.deletePatrolRouteByRouteIds(routeIds);
    }

    /**
     * 删除巡检路线信息
     *
     * @param routeId 巡检路线主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePatrolRouteByRouteId(Long routeId) {
        // 先删除关联的点位关系
        patrolRouteMapper.deleteRoutePointsByRouteId(routeId);
        // 再删除路线
        return patrolRouteMapper.deletePatrolRouteByRouteId(routeId);
    }

    /**
     * 查询巡检路线列表（包含部门名称和关联点位）
     * 注意：此方法会被PageHelper自动分页处理
     *
     * @param listReqVO 巡检路线查询条件
     * @return 巡检路线响应VO集合
     */
    @Override
    public List<PatrolRouteRespVO> listPatrolRoutesWithDetails(PatrolRouteListReqVO listReqVO) {
        // 转换查询条件
        PatrolRoute patrolRoute = convertToPatrolRoute(listReqVO);
        
        // 先查询路线基本信息（会被PageHelper自动分页）
        List<PatrolRouteRespVO> routeList = patrolRouteMapper.selectPatrolRouteListWithDept(patrolRoute);
        
        // 为每个路线查询关联的点位信息（不影响分页）
        for (PatrolRouteRespVO route : routeList) {
            List<PatrolRouteRespVO.PatrolPointInfo> points = 
                patrolRouteMapper.selectPatrolPointsByRouteId(route.getRouteId());
            
            route.setPatrolPoints(points);
        }
        
        return routeList;
    }

    /**
     * 查询巡检路线简单列表
     *
     * @param listReqVO 巡检路线查询条件
     * @return 巡检路线简单响应VO集合
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u") 
    public List<PatrolRouteSimpleRespVO> listSimplePatrolRoutes(PatrolRouteListReqVO listReqVO) {
        // 转换查询条件
        PatrolRoute patrolRoute = convertToPatrolRoute(listReqVO);
        
        return patrolRouteMapper.selectSimplePatrolRouteList(patrolRoute);
    }

    /**
     * 根据ID获取巡检路线详情（包含关联点位）
     *
     * @param routeId 路线ID
     * @return 巡检路线响应VO
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public PatrolRouteRespVO getPatrolRouteWithDetails(Long routeId) {
        PatrolRoute patrolRoute = new PatrolRoute();
        patrolRoute.setRouteId(routeId);

        List<PatrolRouteRespVO> routeList = patrolRouteMapper.selectPatrolRouteListWithDept(patrolRoute);

        if (CollectionUtils.isEmpty(routeList)) {
            return null;
        }
        
        PatrolRouteRespVO route = routeList.get(0);
        List<PatrolRouteRespVO.PatrolPointInfo> points = 
            patrolRouteMapper.selectPatrolPointsByRouteId(routeId);

        route.setPatrolPoints(points);
        
        return route;
    }

    /**
     * 新增巡检路线
     *
     * @param saveReqVO 巡检路线保存请求VO
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int savePatrolRoute(PatrolRouteSaveReqVO saveReqVO) {
        PatrolRoute patrolRoute = new PatrolRoute();
        BeanUtils.copyBeanProp(patrolRoute, saveReqVO);
        
        patrolRoute.setCreateBy(SecurityUtils.getUsername());
        patrolRoute.setCreateTime(DateUtils.getNowDate());
        patrolRoute.setDelFlag("0");
        
        int result = patrolRouteMapper.insertPatrolRoute(patrolRoute);
        
        // 处理关联的点位
        if (!CollectionUtils.isEmpty(saveReqVO.getRoutePoints())) {
            // 优先使用详细的点位信息（包含顺序和状态）
            patrolRouteMapper.batchInsertRoutePointsWithDetails(patrolRoute.getRouteId(), saveReqVO.getRoutePoints());
        } else if (!CollectionUtils.isEmpty(saveReqVO.getPointIds())) {
            // 兼容原有的简单点位ID列表
            patrolRouteMapper.batchInsertRoutePoints(patrolRoute.getRouteId(), saveReqVO.getPointIds(), saveReqVO.getStatus());
        }
        
        return result;
    }

    /**
     * 修改巡检路线
     *
     * @param saveReqVO 巡检路线保存请求VO
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePatrolRoute(PatrolRouteSaveReqVO saveReqVO) {
        PatrolRoute patrolRoute = new PatrolRoute();
        BeanUtils.copyBeanProp(patrolRoute, saveReqVO);
        
        patrolRoute.setUpdateBy(SecurityUtils.getUsername());
        patrolRoute.setUpdateTime(DateUtils.getNowDate());
        
        int result = patrolRouteMapper.updatePatrolRoute(patrolRoute);
        
        // 重新处理关联的点位
        patrolRouteMapper.deleteRoutePointsByRouteId(patrolRoute.getRouteId());
        if (!CollectionUtils.isEmpty(saveReqVO.getRoutePoints())) {
            // 优先使用详细的点位信息（包含顺序和状态）
            patrolRouteMapper.batchInsertRoutePointsWithDetails(patrolRoute.getRouteId(), saveReqVO.getRoutePoints());
        } else if (!CollectionUtils.isEmpty(saveReqVO.getPointIds())) {
            // 兼容原有的简单点位ID列表
            patrolRouteMapper.batchInsertRoutePoints(patrolRoute.getRouteId(), saveReqVO.getPointIds(), saveReqVO.getStatus());
        }
        
        return result;
    }



    /**
     * 转换查询条件
     *
     * @param listReqVO 查询请求VO
     * @return PatrolRoute
     */
    private PatrolRoute convertToPatrolRoute(PatrolRouteListReqVO listReqVO) {
        PatrolRoute patrolRoute = new PatrolRoute();
        if (listReqVO != null) {
            patrolRoute.setRouteName(listReqVO.getRouteName());
            patrolRoute.setDeptId(listReqVO.getDeptId());
            patrolRoute.setStatus(listReqVO.getStatus());
        }
        return patrolRoute;
    }

    /**
     * 校验巡检路线名称是否唯一
     *
     * @param route 巡检路线信息
     * @return 结果
     */
    public boolean validateRouteNameUnique(PatrolRouteSaveReqVO route) {
        int count = patrolRouteMapper.checkRouteNameUnique(route.getRouteName(),
                route.getRouteId() == null ? 0L : route.getRouteId());
        return count == 0;
    }
    /**
     * 校验巡检路线点位是否重复
     *
     * @param route 巡检路线信息
     * @return 校验结果，true表示无重复，false表示有重复
     */
    @Override
    public boolean validateRoutePointsUnique(PatrolRouteSaveReqVO route) {
        if (route.getRoutePoints() != null && !route.getRoutePoints().isEmpty()) {
            // 检查 routePoints 中的点位是否有重复
            Set<Long> pointIds = new HashSet<>();
            for (PatrolRouteSaveReqVO.RoutePointInfo pointInfo : route.getRoutePoints()) {
                if (!pointIds.add(pointInfo.getPointId())) {
                    return false; // 发现重复点位
                }
            }
        } else if (route.getPointIds() != null && !route.getPointIds().isEmpty()) {
            // 检查 pointIds 中的点位是否有重复
            Set<Long> pointIds = new HashSet<>(route.getPointIds());
            if (pointIds.size() != route.getPointIds().size()) {
                return false; // 发现重复点位
            }
        }
        return true; // 无重复点位
    }
}
