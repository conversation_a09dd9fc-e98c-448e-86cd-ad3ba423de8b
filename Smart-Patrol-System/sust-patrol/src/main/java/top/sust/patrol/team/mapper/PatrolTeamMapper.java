package top.sust.patrol.team.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.sust.patrol.team.domain.PatrolTeam;

import java.util.List;

/**
 * 巡逻队伍Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface PatrolTeamMapper extends BaseMapper<PatrolTeam> {
    /**
     * 查询巡逻队伍
     *
     * @param teamId 巡逻队伍主键
     * @return 巡逻队伍
     */
    public PatrolTeam selectPatrolTeamByTeamId(Long teamId);

    /**
     * 查询巡逻队伍列表
     *
     * @param patrolTeam 巡逻队伍
     * @return 巡逻队伍集合
     */
    public List<PatrolTeam> selectPatrolTeamList(PatrolTeam patrolTeam);

    /**
     * 新增巡逻队伍
     *
     * @param patrolTeam 巡逻队伍
     * @return 结果
     */
    public int insertPatrolTeam(PatrolTeam patrolTeam);

    /**
     * 修改巡逻队伍
     *
     * @param patrolTeam 巡逻队伍
     * @return 结果
     */
    public int updatePatrolTeam(PatrolTeam patrolTeam);

    /**
     * 删除巡逻队伍
     *
     * @param teamId 巡逻队伍主键
     * @return 结果
     */
    public int deletePatrolTeamByTeamId(Long teamId);

    /**
     * 批量删除巡逻队伍
     *
     * @param teamIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolTeamByTeamIds(Long[] teamIds);

    /**
     * 检查巡逻队伍名称是否在指定部门下重复
     *
     * @param teamName 巡逻队伍名称
     * @param deptId 部门ID
     * @param teamId 巡逻队伍ID（修改时传入，新增时为null）
     * @return 重复的数量
     */
    public int checkTeamNameDuplicate(@Param("teamName") String teamName, @Param("deptId") Long deptId, @Param("teamId") Long teamId);
}
