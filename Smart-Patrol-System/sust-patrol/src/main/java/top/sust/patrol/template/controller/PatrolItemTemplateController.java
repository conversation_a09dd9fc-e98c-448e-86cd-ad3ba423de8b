package top.sust.patrol.template.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.core.page.TableDataInfo;
import top.sust.common.enums.BusinessType;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.patrol.template.domain.PatrolItemTemplate;
import top.sust.patrol.template.domain.vo.PatrolItemTemplateListReqVO;
import top.sust.patrol.template.domain.vo.PatrolItemTemplateRespVO;
import top.sust.patrol.template.service.IPatrolItemTemplateService;

/**
 * 点位检查项模板接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@Api(tags = "点位检查项模板管理接口")
@RestController
@RequestMapping("/patrol/template")
public class PatrolItemTemplateController extends BaseController
{
    @Autowired
    private IPatrolItemTemplateService patrolItemTemplateService;

    /**
     * 查询点位检查项模板列表
     */
    @ApiOperation(value = "查询点位检查项模板列表")
    @PreAuthorize("@ss.hasPermi('patrol:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(PatrolItemTemplateListReqVO patrolItemTemplate)
    {
        startPage();
        List<PatrolItemTemplate> list = patrolItemTemplateService.selectPatrolItemTemplateList(patrolItemTemplate);
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(BeanUtils.toBean(list, PatrolItemTemplateRespVO.class));
        return dataTable;
    }
//    public AjaxResult list(PatrolItemTemplateListReqVO patrolItemTemplateListReqVO)
//    {
//        List<PatrolItemTemplate> list = patrolItemTemplateService.selectPatrolItemTemplateList(patrolItemTemplateListReqVO);
//        return success(BeanUtils.toBean(list, PatrolItemTemplateRespVO.class));
//    }


    /**
     * 导出点位检查项模板列表
     */
    @ApiOperation(value = "导出点位检查项模板列表")
    @PreAuthorize("@ss.hasPermi('patrol:template:export')")
    @Log(title = "点位检查项模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolItemTemplateListReqVO patrolItemTemplateListReqVO)
    {
        List<PatrolItemTemplate> list = patrolItemTemplateService.selectPatrolItemTemplateList(patrolItemTemplateListReqVO);
        ExcelUtil<PatrolItemTemplate> util = new ExcelUtil<PatrolItemTemplate>(PatrolItemTemplate.class);
        util.exportExcel(response, list, "点位检查项模板数据");
    }


    /**
     * 获取点位检查项模板详细信息
     */
    @ApiOperation(value = "获取点位检查项模板详细信息")
    @PreAuthorize("@ss.hasPermi('patrol:template:query')")
    @GetMapping(value = "/{templateId}")
    public R<PatrolItemTemplateRespVO> getInfo(@PathVariable("templateId") Long templateId)
    {
        PatrolItemTemplate patrolItemTemplate = patrolItemTemplateService.selectPatrolItemTemplateByTemplateId(templateId);
        return R.ok(BeanUtils.toBean(patrolItemTemplate, PatrolItemTemplateRespVO.class));
    }

    /**
     * 新增点位检查项模板
     */
    @ApiOperation(value = "新增点位检查项模板")
    @PreAuthorize("@ss.hasPermi('patrol:template:add')")
    @Log(title = "点位检查项模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatrolItemTemplate patrolItemTemplate)
    {
        return toAjax(patrolItemTemplateService.insertPatrolItemTemplate(patrolItemTemplate));
    }

    /**
     * 修改点位检查项模板
     */
    @ApiOperation(value = "修改点位检查项模板")
    @PreAuthorize("@ss.hasPermi('patrol:template:edit')")
    @Log(title = "点位检查项模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatrolItemTemplate patrolItemTemplate)
    {
        return toAjax(patrolItemTemplateService.updatePatrolItemTemplate(patrolItemTemplate));
    }

    /**
     * 删除点位检查项模板
     */
    @ApiOperation(value = "删除点位检查项模板")
    @PreAuthorize("@ss.hasPermi('patrol:template:remove')")
    @Log(title = "点位检查项模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds)
    {
        if (templateIds == null || templateIds.length == 0) {
            return warn("请选择需要删除的模板");
        }
        return toAjax(patrolItemTemplateService.deletePatrolItemTemplateByTemplateIds(templateIds));
    }
}
