package top.sust.patrol.point.domain;

import java.math.BigDecimal;
import java.util.List;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import top.sust.common.core.domain.BaseEntity;

/**
 * 巡检点位对象 patrol_point
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.point.mapper.PatrolPointMapper.PatrolPointResult")
@ApiModel(value = "PatrolPoint", description = "巡检点位对象")
public class PatrolPoint extends BaseEntity{
    private static final long serialVersionUID = 1L;
    /** 点位ID */
    @TableId(value = "point_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "${comment}", name = "pointId")
    private Long pointId;
    /** 点位名称 */
    @Excel(name = "点位名称" )
    @ApiModelProperty(value = "点位名称", name = "pointName")
    private String pointName;
    /** 区域ID */
    @Excel(name = "区域ID" )
    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;
    /** 信标ID */
    @Excel(name = "信标ID" )
    @ApiModelProperty(value = "信标ID", name = "beaconId")
    private Long beaconId;
    /** 信标名称 */
    @ApiModelProperty(value = "信标名称", name = "beaconName")
    private String beaconName;
    /** 经度 */
    @Excel(name = "经度" )
    @ApiModelProperty(value = "经度", name = "longtitude")
    private BigDecimal longtitude;
    /** 纬度 */
    @Excel(name = "纬度" )
    @ApiModelProperty(value = "纬度", name = "latitude")
    private BigDecimal latitude;
    /** x坐标 */
    @Excel(name = "x坐标" )
    @ApiModelProperty(value = "x坐标", name = "locationX")
    private BigDecimal locationX;
    /** y坐标 */
    @Excel(name = "y坐标" )
    @ApiModelProperty(value = "y坐标", name = "locationY")
    private BigDecimal locationY;
    /** 图层 */
    @Excel(name = "图层" )
    @ApiModelProperty(value = "图层", name = "locationLayer")
    private Long locationLayer;
    /** 状态 */
    @Excel(name = "状态" )
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /** 逻辑删除 */
    @ApiModelProperty(value = "状态", name = "delFlag")
    private String delFlag;
    /** 巡检点位项目信息 */
    private List<PatrolPointItem> patrolPointItemList;
}
