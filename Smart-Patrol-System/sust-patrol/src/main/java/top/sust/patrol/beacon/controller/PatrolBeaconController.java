package top.sust.patrol.beacon.controller;

import java.util.Collections;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.core.domain.entity.SysUser;
import top.sust.common.enums.BusinessType;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.patrol.area.domain.vo.PatrolAreaRespVO;
import top.sust.patrol.beacon.domain.PatrolBeacon;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconListReqVO;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconRespVO;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconSaveReqVO;
import top.sust.patrol.beacon.service.IPatrolBeaconService;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.common.core.page.TableDataInfo;
import top.sust.patrol.point.domain.PatrolPoint;
import top.sust.patrol.point.domain.vo.PatrolPointListReqVO;
import top.sust.patrol.point.domain.vo.PatrolPointRespVO;
import top.sust.patrol.point.mapper.PatrolPointMapper;

import static top.sust.framework.datasource.DynamicDataSourceContextHolder.log;

/**
 * 信标管理接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "信标管理管理接口")
@RestController
@RequestMapping("/beacon/beacon")
public class PatrolBeaconController extends BaseController
{
    @Autowired
    private IPatrolBeaconService patrolBeaconService;

    @Autowired
    private PatrolPointMapper patrolPointMapper;

    /**
     * 查询信标管理列表
     */
    @ApiOperation(value = "查询信标管理列表")
    @PreAuthorize("@ss.hasPermi('beacon:beacon:list')")
    @GetMapping("/list")
    public TableDataInfo list(PatrolBeaconListReqVO patrolBeaconListReqVO)
    {
        startPage();
        List<PatrolBeacon> list = patrolBeaconService.selectPatrolBeaconList(patrolBeaconListReqVO);
        TableDataInfo dataTable =getDataTable(list);
        dataTable.setRows(BeanUtils.toBean(list, PatrolBeaconRespVO.class));
        return dataTable;
    }

    /**
     * 导出信标管理列表
     */
    @ApiOperation(value = "导出信标管理列表")
    @PreAuthorize("@ss.hasPermi('beacon:beacon:export')")
    @Log(title = "信标管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolBeaconListReqVO patrolBeaconListReqVO)
    {
        List<PatrolBeacon> list = patrolBeaconService.selectPatrolBeaconList(patrolBeaconListReqVO);
        ExcelUtil<PatrolBeacon> util = new ExcelUtil<PatrolBeacon>(PatrolBeacon.class);
        util.exportExcel(response, list, "信标管理数据");
    }

    /**
     * 获取信标管理详细信息
     */
    @ApiOperation(value = "获取信标管理详细信息")
    @PreAuthorize("@ss.hasPermi('beacon:beacon:query')")
    @GetMapping(value = "/{beaconId}")
    public R<PatrolBeaconRespVO> getInfo(@PathVariable("beaconId") Long beaconId)
    {
        return R.ok(BeanUtils.toBean(patrolBeaconService.selectPatrolBeaconByBeaconId(beaconId), PatrolBeaconRespVO.class));
    }

    /**
     * 新增信标管理
     */
    @ApiOperation(value = "新增信标管理")
    @PreAuthorize("@ss.hasPermi('beacon:beacon:add')")
    @Log(title = "信标管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatrolBeaconSaveReqVO patrolBeacon) {
        try {
            return toAjax(patrolBeaconService.insertPatrolBeacon(patrolBeacon));
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改信标管理
     */
    @ApiOperation(value = "修改信标管理")
    @PreAuthorize("@ss.hasPermi('beacon:beacon:edit')")
    @Log(title = "信标管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatrolBeaconSaveReqVO patrolBeacon) {
        try {
            return toAjax(patrolBeaconService.updatePatrolBeacon(patrolBeacon));
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除信标管理
     */
    @ApiOperation(value = "删除信标管理")
    @PreAuthorize("@ss.hasPermi('beacon:beacon:remove')")
    @Log(title = "信标管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{beaconIds}")
    public AjaxResult remove(@PathVariable Long[] beaconIds)
    {
        return toAjax(patrolBeaconService.deletePatrolBeaconByBeaconIds(beaconIds));
    }

    /**
     * 导入信标信息
     */
    @Log(title = "信标导入管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('beacon:beacon:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<PatrolBeacon> util = new ExcelUtil<PatrolBeacon>(PatrolBeacon.class);
        List<PatrolBeacon> beaconList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = patrolBeaconService.importBeacon(beaconList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<PatrolBeacon> util = new ExcelUtil<PatrolBeacon>(PatrolBeacon.class);
        util.importTemplateExcel(response, "信标数据");
    }
}
