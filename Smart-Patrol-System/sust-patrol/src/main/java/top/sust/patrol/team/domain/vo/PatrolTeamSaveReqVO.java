package top.sust.patrol.team.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 巡逻队伍保存请求VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ApiModel(value = "PatrolTeamSaveReqVO", description = "巡逻队伍保存请求VO")
public class PatrolTeamSaveReqVO {

    /** 巡逻队ID */
    @ApiModelProperty(value = "巡逻队ID", name = "teamId")
    private Long teamId;

    /** 巡逻队名称 */
    @NotBlank(message = "巡逻队名称不能为空")
    @ApiModelProperty(value = "巡逻队名称", name = "teamName")
    private String teamName;

    /** 部门ID */
    @NotNull(message = "部门ID不能为空")
    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    /** 队伍成员用户ID列表 */
    @ApiModelProperty(value = "队伍成员用户ID列表", name = "memberIds")
    private Long[] memberIds;
}
