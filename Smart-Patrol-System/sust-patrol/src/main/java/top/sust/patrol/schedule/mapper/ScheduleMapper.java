package top.sust.patrol.schedule.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import top.sust.patrol.schedule.domain.Schedule;
import top.sust.patrol.schedule.domain.vo.ScheduleListReqVO;
import top.sust.patrol.schedule.domain.vo.ScheduleRespVO;
import top.sust.patrol.schedule.domain.vo.ScheduleSimpleRespVO;

/**
 * 计划表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface ScheduleMapper extends BaseMapper<Schedule> {
    /**
     * 查询计划表
     *
     * @param scheduleId 计划表主键
     * @return 计划表
     */
    public Schedule selectScheduleByScheduleId(Long scheduleId);

    /**
     * 查询计划表列表
     *
     * @param schedule 计划表
     * @return 计划表集合
     */
    public List<Schedule> selectScheduleList(Schedule schedule);

    /**
     * 新增计划表
     *
     * @param schedule 计划表
     * @return 结果
     */
    public int insertSchedule(Schedule schedule);

    /**
     * 修改计划表
     *
     * @param schedule 计划表
     * @return 结果
     */
    public int updateSchedule(Schedule schedule);

    /**
     * 删除计划表
     *
     * @param scheduleId 计划表主键
     * @return 结果
     */
    public int deleteScheduleByScheduleId(Long scheduleId);

    /**
     * 批量删除计划表
     *
     * @param scheduleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduleByScheduleIds(Long[] scheduleIds);

    /**
     * 查询巡检计划列表（带关联信息）
     *
     * @param reqVO 查询条件
     * @return 巡检计划列表
     */
    public List<ScheduleRespVO> selectScheduleRespList(ScheduleListReqVO reqVO);

    /**
     * 查询巡检计划详情（带关联信息）
     *
     * @param scheduleId 计划ID
     * @return 巡检计划详情
     */
    public ScheduleRespVO selectScheduleRespByScheduleId(Long scheduleId);

    /**
     * 查询巡检计划简单列表
     *
     * @return 巡检计划简单列表
     */
    public List<ScheduleSimpleRespVO> selectScheduleSimpleList();

    /**
     * 查询启用的巡检计划（用于定时任务）
     *
     * @param dayOfWeek 星期几（1-7）
     * @return 启用的巡检计划列表
     */
    public List<Schedule> selectEnabledSchedulesByDayOfWeek(Integer dayOfWeek);
}
