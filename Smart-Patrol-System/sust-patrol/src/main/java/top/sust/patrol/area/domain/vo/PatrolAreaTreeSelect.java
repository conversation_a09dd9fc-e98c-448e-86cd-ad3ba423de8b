package top.sust.patrol.area.domain.vo;

import top.sust.common.constant.UserConstants;
import top.sust.common.core.domain.TreeSelect;
import top.sust.common.utils.StringUtils;
import top.sust.patrol.area.domain.PatrolArea;

import java.util.stream.Collectors;

public class PatrolAreaTreeSelect extends TreeSelect {

    public PatrolAreaTreeSelect(PatrolArea patrolArea) {
        setId(patrolArea.getAreaId());
        setLabel(patrolArea.getAreaName());
        setDisabled(StringUtils.equals(UserConstants.DEPT_DISABLE, patrolArea.getStatus()));
        setChildren(patrolArea.getChildren().stream().map(obj -> new PatrolAreaTreeSelect((PatrolArea) obj)).collect(Collectors.toList()));
    }

}
