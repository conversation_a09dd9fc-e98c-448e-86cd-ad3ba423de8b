package top.sust.patrol.beacon.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import top.sust.patrol.beacon.domain.PatrolBeacon;
import top.sust.patrol.beacon.domain.vo.PatrolBeaconListReqVO;

/**
 * 信标管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface PatrolBeaconMapper extends BaseMapper<PatrolBeacon> {
    /**
     * 查询信标管理
     *
     * @param beaconId 信标管理主键
     * @return 信标管理
     */
    public PatrolBeacon selectPatrolBeaconByBeaconId(Long beaconId);


    /**
     * 查询信标管理列表
     *
     * @param patrolBeacon 信标管理
     * @return 信标管理集合
     */
    public List<PatrolBeacon> selectPatrolBeaconList(PatrolBeaconListReqVO patrolBeacon);

    /**
     * 新增信标管理
     *
     * @param patrolBeacon 信标管理
     * @return 结果
     */
    public int insertPatrolBeacon(PatrolBeacon patrolBeacon);

    /**
     * 修改信标管理
     *
     * @param patrolBeacon 信标管理
     * @return 结果
     */
    public int updatePatrolBeacon(PatrolBeacon patrolBeacon);

    /**
     * 删除信标管理
     *
     * @param beaconId 信标管理主键
     * @return 结果
     */
    public int deletePatrolBeaconByBeaconId(Long beaconId);

    /**
     * 批量删除信标管理
     *
     * @param beaconIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolBeaconByBeaconIds(Long[] beaconIds);
}
