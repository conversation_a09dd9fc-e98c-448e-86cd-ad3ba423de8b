package top.sust.patrol.area.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.enums.BusinessType;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.patrol.area.domain.PatrolArea;
import top.sust.patrol.area.domain.vo.PatrolAreaListReqVO;
import top.sust.patrol.area.domain.vo.PatrolAreaRespVO;
import top.sust.patrol.area.domain.vo.PatrolAreaSaveReqVO;
import top.sust.patrol.area.service.IPatrolAreaService;
import top.sust.common.utils.poi.ExcelUtil;

/**
 * 巡检区域接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "巡检区域管理接口")
@RestController
@RequestMapping("/patrol/area")
public class PatrolAreaController extends BaseController
{
    @Autowired
    private IPatrolAreaService patrolAreaService;

    /**
     * 查询巡检区域列表
     */
    @ApiOperation(value = "查询巡检区域列表")
    @PreAuthorize("@ss.hasPermi('patrol:area:list')")
    @GetMapping("/list")
    public AjaxResult list(PatrolAreaListReqVO patrolArea) {
        List<PatrolArea> list = patrolAreaService.selectPatrolAreaList(patrolArea);
        return success(BeanUtils.toBean(list, PatrolAreaRespVO.class));
    }

    /**
     * 构建前端所需要下拉树结构
     * @return
     */
    @ApiOperation(value = "构建区域下拉树结构")
    @PreAuthorize("@ss.hasPermi('patrol:area:list')")
    @GetMapping("/tree")
    public AjaxResult areaTree(PatrolAreaListReqVO patrolArea) {
        return AjaxResult.success(patrolAreaService.selectAreaTreeList(patrolArea));
    }

    /**
     * 导出巡检区域列表
     */
    @ApiOperation(value = "导出巡检区域列表")
    @PreAuthorize("@ss.hasPermi('patrol:area:export')")
    @Log(title = "巡检区域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolAreaListReqVO patrolArea)
    {
        List<PatrolArea> list = patrolAreaService.selectPatrolAreaList(patrolArea);
        ExcelUtil<PatrolArea> util = new ExcelUtil<PatrolArea>(PatrolArea.class);
        util.exportExcel(response, list, "巡检区域数据");
    }

    /**
     * 获取巡检区域详细信息
     */
    @ApiOperation(value = "获取巡检区域详细信息")
    @PreAuthorize("@ss.hasPermi('patrol:area:query')")
    @GetMapping(value = "/{areaId}")
    public R<PatrolAreaRespVO> getInfo(@PathVariable("areaId") Long areaId)
    {
        return R.ok(BeanUtils.toBean(patrolAreaService.selectPatrolAreaByAreaId(areaId), PatrolAreaRespVO.class));
    }

    /**
     * 新增巡检区域
     */
    @ApiOperation(value = "新增巡检区域")
    @PreAuthorize("@ss.hasPermi('patrol:area:add')")
    @Log(title = "巡检区域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody PatrolAreaSaveReqVO patrolArea)
    {
        return toAjax(patrolAreaService.insertPatrolArea(patrolArea));
    }

    /**
     * 修改巡检区域
     */
    @ApiOperation(value = "修改巡检区域")
    @PreAuthorize("@ss.hasPermi('patrol:area:edit')")
    @Log(title = "巡检区域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody PatrolAreaSaveReqVO patrolArea)
    {
        return toAjax(patrolAreaService.updatePatrolArea(patrolArea));
    }

    /**
     * 删除巡检区域
     */
    @ApiOperation(value = "删除巡检区域")
    @PreAuthorize("@ss.hasPermi('patrol:area:remove')")
    @Log(title = "巡检区域", businessType = BusinessType.DELETE)
	@DeleteMapping("/{areaIds}")
    public AjaxResult remove(@PathVariable Long[] areaIds) {
        if (areaIds == null || areaIds.length == 0)
            return warn("请选择需要删除的区域");
        for (Long areaId : areaIds) {
            if (patrolAreaService.hasChildByAreaId(areaId))
            {
                return warn("存在下级区域,不允许删除");
            }
        }
        return toAjax(patrolAreaService.deletePatrolAreaByAreaIds(areaIds));
    }
}
