package top.sust.patrol.point.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import top.sust.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 巡检点位项目对象 patrol_point_item
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.point.mapper.PatrolPointMapper.PatrolPointResult")
@ApiModel(value = "PatrolPointItem", description = "巡检点位项目对象")
public class PatrolPointItem extends BaseEntity {
private static final long serialVersionUID = 1L;

    /** 检查项ID */
    @ApiModelProperty(value = "检查项ID", name = "itemId")
    private Long itemId;

    /** 检查项名称 */
    @Excel(name = "检查项名称")
    @ApiModelProperty(value = "检查项名称", name = "itemName")
    private String itemName;

    /** 检查项描述 */
    @Excel(name = "检查项描述")
    @ApiModelProperty(value = "检查项描述", name = "description")
    private String description;

    /** 点位ID */
    @Excel(name = "点位ID")
    @ApiModelProperty(value = "点位ID", name = "pointId")
    private Long pointId;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    /** 逻辑删除 */
    @ApiModelProperty(value = "状态", name = "delFlag")
    private String delFlag;
}
