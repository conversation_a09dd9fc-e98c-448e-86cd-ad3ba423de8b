package top.sust.patrol.schedule.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.enums.BusinessType;
import top.sust.patrol.schedule.domain.Schedule;
import top.sust.patrol.schedule.domain.vo.ScheduleRespVO;
import top.sust.patrol.schedule.service.IScheduleService;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.common.core.page.TableDataInfo;

/**
 * 计划表接口
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Api(tags = "计划表管理接口")
@RestController
@RequestMapping("/schedule/schedule")
public class ScheduleController extends BaseController
{
    @Autowired
    private IScheduleService scheduleService;

    /**
     * 查询计划表列表
     */
    @ApiOperation(value = "查询计划表列表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:list')")
    @GetMapping("/list")
    public TableDataInfo list(Schedule schedule)
    {
        startPage();
        List<Schedule> list = scheduleService.selectScheduleList(schedule);
        return getDataTable(list);
    }

    /**
     * 导出计划表列表
     */
    @ApiOperation(value = "导出计划表列表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:export')")
    @Log(title = "计划表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Schedule schedule)
    {
        List<Schedule> list = scheduleService.selectScheduleList(schedule);
        ExcelUtil<Schedule> util = new ExcelUtil<Schedule>(Schedule.class);
        util.exportExcel(response, list, "计划表数据");
    }

    /**
     * 获取巡检计划详细信息
     */
    @ApiOperation(value = "获取巡检计划详细信息")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:query')")
    @GetMapping(value = "/{scheduleId}")
    public R<ScheduleRespVO> getInfo(@PathVariable("scheduleId") Long scheduleId)
    {
        return R.ok(scheduleService.selectScheduleRespByScheduleId(scheduleId));
    }

    /**
     * 新增计划表
     */
    @ApiOperation(value = "新增计划表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:add')")
    @Log(title = "计划表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Schedule schedule)
    {
        return toAjax(scheduleService.insertSchedule(schedule));
    }

    /**
     * 修改计划表
     */
    @ApiOperation(value = "修改计划表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:edit')")
    @Log(title = "计划表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Schedule schedule)
    {
        return toAjax(scheduleService.updateSchedule(schedule));
    }

    /**
     * 删除计划表
     */
    @ApiOperation(value = "删除计划表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:remove')")
    @Log(title = "计划表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scheduleIds}")
    public AjaxResult remove(@PathVariable Long[] scheduleIds)
    {
        return toAjax(scheduleService.deleteScheduleByScheduleIds(scheduleIds));
    }
}
