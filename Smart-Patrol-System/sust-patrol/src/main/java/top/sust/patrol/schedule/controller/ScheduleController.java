package top.sust.patrol.schedule.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.enums.BusinessType;
import top.sust.patrol.schedule.domain.Schedule;
import top.sust.patrol.schedule.domain.vo.ScheduleListReqVO;
import top.sust.patrol.schedule.domain.vo.ScheduleRespVO;
import top.sust.patrol.schedule.domain.vo.ScheduleSaveReqVO;
import top.sust.patrol.schedule.domain.vo.ScheduleSimpleRespVO;
import top.sust.patrol.route.domain.vo.PatrolRouteSimpleRespVO;
import top.sust.patrol.team.domain.PatrolTeam;
import top.sust.patrol.route.service.IPatrolRouteService;
import top.sust.patrol.team.service.IPatrolTeamService;
import top.sust.patrol.schedule.service.IScheduleService;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.common.core.page.TableDataInfo;

import javax.validation.Valid;
import java.util.List;

/**
 * 计划表接口
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Api(tags = "计划表管理接口")
@RestController
@RequestMapping("/schedule/schedule")
public class ScheduleController extends BaseController
{
    @Autowired
    private IScheduleService scheduleService;

    @Autowired
    private IPatrolRouteService patrolRouteService;

    @Autowired
    private IPatrolTeamService patrolTeamService;

    /**
     * 查询巡检计划列表
     */
    @ApiOperation(value = "查询巡检计划列表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScheduleListReqVO listReqVO)
    {
        startPage();
        List<ScheduleRespVO> list = scheduleService.selectScheduleRespList(listReqVO);
        return getDataTable(list);
    }

    /**
     * 导出巡检计划列表
     */
    @ApiOperation(value = "导出巡检计划列表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:export')")
    @Log(title = "巡检计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScheduleListReqVO listReqVO)
    {
        List<ScheduleRespVO> list = scheduleService.selectScheduleRespList(listReqVO);
        ExcelUtil<ScheduleRespVO> util = new ExcelUtil<ScheduleRespVO>(ScheduleRespVO.class);
        util.exportExcel(response, list, "巡检计划数据");
    }

    /**
     * 获取巡检计划详细信息
     */
    @ApiOperation(value = "获取巡检计划详细信息")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:query')")
    @GetMapping(value = "/{scheduleId}")
    public R<ScheduleRespVO> getInfo(@PathVariable("scheduleId") Long scheduleId)
    {
        return R.ok(scheduleService.selectScheduleRespByScheduleId(scheduleId));
    }

    /**
     * 新增巡检计划
     */
    @ApiOperation(value = "新增巡检计划")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:add')")
    @Log(title = "巡检计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody ScheduleSaveReqVO saveReqVO)
    {
        return toAjax(scheduleService.insertScheduleByVO(saveReqVO));
    }

    /**
     * 修改巡检计划
     */
    @ApiOperation(value = "修改巡检计划")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:edit')")
    @Log(title = "巡检计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody ScheduleSaveReqVO saveReqVO)
    {
        return toAjax(scheduleService.updateScheduleByVO(saveReqVO));
    }

    /**
     * 删除计划表
     */
    @ApiOperation(value = "删除计划表")
    @PreAuthorize("@ss.hasPermi('schedule:schedule:remove')")
    @Log(title = "计划表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scheduleIds}")
    public AjaxResult remove(@PathVariable Long[] scheduleIds)
    {
        return toAjax(scheduleService.deleteScheduleByScheduleIds(scheduleIds));
    }

    /**
     * 获取巡检计划简单列表
     */
    @ApiOperation(value = "获取巡检计划简单列表")
    @GetMapping("/simple-list")
    public R<List<ScheduleSimpleRespVO>> getSimpleList()
    {
        List<ScheduleSimpleRespVO> list = scheduleService.selectScheduleSimpleList();
        return R.ok(list);
    }

  
}
