package top.sust.patrol.route.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import top.sust.patrol.route.domain.PatrolRoute;
import top.sust.patrol.route.domain.vo.PatrolRouteSaveReqVO;
import top.sust.patrol.route.domain.vo.PatrolRouteSimpleRespVO;
import top.sust.patrol.route.domain.vo.PatrolRouteRespVO;
import org.apache.ibatis.annotations.Param;

/**
 * 巡检路线Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface PatrolRouteMapper extends BaseMapper<PatrolRoute> {
    /**
     * 查询巡检路线
     *
     * @param routeId 巡检路线主键
     * @return 巡检路线
     */
    public PatrolRouteRespVO selectPatrolRouteByRouteId(Long routeId);

    /**
     * 查询巡检路线列表
     *
     * @param patrolRoute 巡检路线
     * @return 巡检路线集合
     */
    public List<PatrolRoute> selectPatrolRouteList(PatrolRoute patrolRoute);

    /**
     * 查询巡检路线列表（包含部门名称）
     *
     * @param patrolRoute 巡检路线
     * @return 巡检路线集合
     */
    public List<PatrolRouteRespVO> selectPatrolRouteListWithDept(PatrolRoute patrolRoute);

    /**
     * 根据路线ID查询关联的点位信息
     *
     * @param routeId 路线ID
     * @return 点位信息列表
     */
    public List<PatrolRouteRespVO.PatrolPointInfo> selectPatrolPointsByRouteId(Long routeId);

    /**
     * 查询巡检路线简单列表
     *
     * @param patrolRoute 巡检路线
     * @return 巡检路线简单响应VO集合
     */
    public List<PatrolRouteSimpleRespVO> selectSimplePatrolRouteList(PatrolRoute patrolRoute);

    /**
     * 新增巡检路线
     *
     * @param patrolRoute 巡检路线
     * @return 结果
     */
    public int insertPatrolRoute(PatrolRoute patrolRoute);

    /**
     * 修改巡检路线
     *
     * @param patrolRoute 巡检路线
     * @return 结果
     */
    public int updatePatrolRoute(PatrolRoute patrolRoute);

    /**
     * 删除巡检路线
     *
     * @param routeId 巡检路线主键
     * @return 结果
     */
    public int deletePatrolRouteByRouteId(Long routeId);

    /**
     * 批量删除巡检路线
     *
     * @param routeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolRouteByRouteIds(Long[] routeIds);

    /**
     * 批量插入路线点位关联关系
     *
     * @param routeId 路线ID
     * @param pointIds 点位ID列表
     * @param status 状态
     * @return 结果
     */
    public int batchInsertRoutePoints(@Param("routeId") Long routeId, @Param("pointIds") List<Long> pointIds, @Param("status") String status);

    /**
     * 批量插入路线点位关联关系（带详细信息）
     *
     * @param routeId 路线ID
     * @param routePoints 路线点位详细信息列表
     * @return 结果
     */
    public int batchInsertRoutePointsWithDetails(@Param("routeId") Long routeId, @Param("routePoints") List<PatrolRouteSaveReqVO.RoutePointInfo> routePoints);

    /**
     * 删除路线点位关联关系
     *
     * @param routeId 路线ID
     * @return 结果
     */
    public int deleteRoutePointsByRouteId(Long routeId);

    /**
     * 检查路线名称是否唯一
     *
     * @param routeName 路线名称
     * @param routeId 路线ID（更新时使用）
     * @return 结果
     */
    public int checkRouteNameUnique(@Param("routeName") String routeName, @Param("routeId") Long routeId);
}
