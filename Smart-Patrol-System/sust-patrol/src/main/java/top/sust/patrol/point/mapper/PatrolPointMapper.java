package top.sust.patrol.point.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import top.sust.patrol.point.domain.PatrolPoint;
import top.sust.patrol.point.domain.PatrolPointItem;
import top.sust.patrol.point.domain.vo.PatrolPointListReqVO;

/**
 * 巡检点位Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface PatrolPointMapper extends BaseMapper<PatrolPoint> {
    /**
     * 查询巡检点位
     *
     * @param pointId 巡检点位主键
     * @return 巡检点位
     */
    public PatrolPoint selectPatrolPointByPointId(Long pointId);

    /**
     * 查询巡检点位列表
     *
     * @param patrolPoint 巡检点位
     * @return 巡检点位集合
     */
    public List<PatrolPoint> selectPatrolPointList(PatrolPointListReqVO patrolPoint);

    /**
     * 新增巡检点位
     *
     * @param patrolPoint 巡检点位
     * @return 结果
     */
    public int insertPatrolPoint(PatrolPoint patrolPoint);

    /**
     * 修改巡检点位
     *
     * @param patrolPoint 巡检点位
     * @return 结果
     */
    public int updatePatrolPoint(PatrolPoint patrolPoint);

    /**
     * 批量删除巡检点位信息
     *
     * @param pointIds 需要删除的巡检点位主键数组
     * @return 结果
     */
    public int deletePatrolPointByPointIds(Long[] pointIds);

    /**
     * 批量删除巡检点位项目
     *
     * @param pointIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolPointItemByPointIds(Long[] pointIds);

    /**
     * 批量新增巡检点位项目
     *
     * @param patrolPointItemList 巡检点位项目列表
     * @return 结果
     */
    public int batchPatrolPointItem(List<PatrolPointItem> patrolPointItemList);

    /**
     * 通过巡检点位主键删除巡检点位项目信息
     *
     * @param pointId 巡检点位ID
     * @return 结果
     */
    public int deletePatrolPointItemByPointId(Long pointId);

    /**
     * 根据信标ID查询巡检点位
     *
     * @param beaconId 信标ID
     * @return 巡检点位集合
     */
    public List<PatrolPoint> selectPatrolPointByBeaconId(Long beaconId);

    /**
     * 检查点位是否被巡检路线使用
     *
     * @param pointIds 点位ID数组
     * @return 使用该点位的路线数量
     */
    public int countRoutesUsingPoints(Long[] pointIds);
}
