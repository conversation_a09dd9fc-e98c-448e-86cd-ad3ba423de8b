package top.sust.patrol.point.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
public class PatrolPointListReqVO {
    
    @ApiModelProperty(value = "点位名称", name = "pointName")
    private String pointName;
    
    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;

    @ApiModelProperty(value = "信标ID列表", name = "beaconIds")
    private List<Long> beaconIds;

    @ApiModelProperty(value = "信标名称", name = "beaconName")
    private String beaconName;
    
    @ApiModelProperty(value = "图层", name = "locationLayer")
    private Long locationLayer;
    
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
}
