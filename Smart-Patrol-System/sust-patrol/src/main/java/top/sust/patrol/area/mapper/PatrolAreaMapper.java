package top.sust.patrol.area.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import top.sust.patrol.area.domain.PatrolArea;
import top.sust.patrol.area.domain.vo.PatrolAreaListReqVO;

/**
 * 巡检区域Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface PatrolAreaMapper extends BaseMapper<PatrolArea> {

    public List<PatrolArea> selectChildrenAreaById(Long areaId);

    public int updateAreaChildren(@Param("areas")List<PatrolArea> areaList);

    public int hasChildByAreaId(Long areaId);

    /**
     * 查询巡检区域
     *
     * @param areaId 巡检区域主键
     * @return 巡检区域
     */
    public PatrolArea selectPatrolAreaByAreaId(Long areaId);

    /**
     * 查询巡检区域列表
     *
     * @param patrolArea 巡检区域
     * @return 巡检区域集合
     */
    public List<PatrolArea> selectPatrolAreaList(PatrolAreaListReqVO patrolArea);

    /**
     * 新增巡检区域
     *
     * @param patrolArea 巡检区域
     * @return 结果
     */
    public int insertPatrolArea(PatrolArea patrolArea);

    /**
     * 修改巡检区域
     *
     * @param patrolArea 巡检区域
     * @return 结果
     */
    public int updatePatrolArea(PatrolArea patrolArea);

    /**
     * 删除巡检区域
     *
     * @param areaId 巡检区域主键
     * @return 结果
     */
    public int deletePatrolAreaByAreaId(Long areaId);

    /**
     * 批量删除巡检区域
     *
     * @param areaIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolAreaByAreaIds(Long[] areaIds);
}
