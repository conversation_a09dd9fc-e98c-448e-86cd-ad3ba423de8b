package top.sust.patrol.area.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import top.sust.common.core.domain.TreeEntity;

/**
 * 巡检区域对象 patrol_area
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.area.mapper.PatrolAreaMapper.PatrolAreaResult")
@ApiModel(value = "PatrolArea", description = "巡检区域对象")
public class PatrolArea extends TreeEntity{
private static final long serialVersionUID=1L;
    public static final Long PARENT_ID_ROOT = 0L;

    /** 区域ID */
    @TableId(value = "area_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;
    /** 父区域ID（一级区域的pid默认为0） */
    @ApiModelProperty(value = "父区域ID", name = "areaPid")
    private Long areaPid;
    /** 区域名称 */
    @Excel(name = "区域名称" )
    @ApiModelProperty(value = "区域名称", name = "areaName")
    private String areaName;
    /** 部门ID */
    @Excel(name = "部门ID" )
    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;
    /** 状态 */
    @Excel(name = "状态" )
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /** 逻辑删除 */
    @ApiModelProperty(value = "状态", name = "delFlag")
    private String delFlag;
}
