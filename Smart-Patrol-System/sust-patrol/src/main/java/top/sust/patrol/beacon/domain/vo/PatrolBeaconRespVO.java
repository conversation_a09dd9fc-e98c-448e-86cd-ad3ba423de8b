package top.sust.patrol.beacon.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class PatrolBeaconRespVO {

    @ApiModelProperty(value = "${comment}", name = "beaconId")
    private Long beaconId;

    @ApiModelProperty(value = "逻辑编号", name = "beaconName")
    private String beaconName;

    @ApiModelProperty(value = "信标mac", name = "beaconMac")
    private String beaconMac;

    @ApiModelProperty(value = "UUID", name = "beaconUuid")
    private String beaconUuid;

    @ApiModelProperty(value = "Major UUID", name = "beaconMajor")
    private String beaconMajor;

    @ApiModelProperty(value = "Minor UUID", name = "beaconMinor")
    private String beaconMinor;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

}
