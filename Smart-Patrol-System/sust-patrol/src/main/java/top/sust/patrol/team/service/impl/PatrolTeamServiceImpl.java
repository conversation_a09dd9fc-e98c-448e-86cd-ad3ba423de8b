package top.sust.patrol.team.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.sust.common.annotation.DataScope;
import top.sust.common.utils.DateUtils;
import top.sust.common.utils.SecurityUtils;
import top.sust.patrol.team.domain.PatrolTeam;
import top.sust.patrol.team.domain.PatrolTeamMember;
import top.sust.patrol.team.domain.vo.PatrolTeamDetailRespVO;
import top.sust.patrol.team.domain.vo.PatrolTeamListReqVO;
import top.sust.patrol.team.domain.vo.PatrolTeamListRespVO;
import top.sust.patrol.team.domain.vo.PatrolTeamSaveReqVO;
import top.sust.patrol.team.mapper.PatrolTeamMapper;
import top.sust.patrol.team.mapper.PatrolTeamMemberMapper;
import top.sust.patrol.team.service.IPatrolTeamService;

import java.util.ArrayList;
import java.util.List;

/**
 * 巡逻队伍Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class PatrolTeamServiceImpl extends ServiceImpl<PatrolTeamMapper, PatrolTeam> implements IPatrolTeamService {
    @Autowired
    private PatrolTeamMapper patrolTeamMapper;
    @Autowired
    private PatrolTeamMemberMapper patrolTeamMemberMapper;

    /**
     * 查询巡逻队伍详情
     *
     * @param teamId 巡逻队伍主键
     * @return 巡逻队伍详情
     */
    @Override
    public PatrolTeamDetailRespVO selectPatrolTeamByTeamId(Long teamId) {
        PatrolTeam team = patrolTeamMapper.selectPatrolTeamByTeamId(teamId);
        if (team == null) {
            return null;
        }

        PatrolTeamDetailRespVO respVO = new PatrolTeamDetailRespVO();
        BeanUtils.copyProperties(team, respVO);

        // 查询成员ID列表
        List<Long> memberIds = patrolTeamMemberMapper.selectUserIdsByTeamId(teamId);
        respVO.setMemberIds(memberIds.toArray(new Long[0]));

        // 设置成员列表
        respVO.setMembers(team.getMembers());

        return respVO;
    }

    /**
     * 查询巡逻队伍列表
     *
     * @param reqVO 查询条件
     * @return 巡逻队伍列表
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<PatrolTeamListRespVO> selectPatrolTeamList(PatrolTeamListReqVO reqVO) {
        // 转换查询条件
        PatrolTeam patrolTeam = new PatrolTeam();
        BeanUtils.copyProperties(reqVO, patrolTeam);

        List<PatrolTeam> teamList = patrolTeamMapper.selectPatrolTeamList(patrolTeam);
        List<PatrolTeamListRespVO> respList = new ArrayList<>();

        for (PatrolTeam team : teamList) {
            PatrolTeamListRespVO respVO = new PatrolTeamListRespVO();
            BeanUtils.copyProperties(team, respVO);
            respList.add(respVO);
        }

        return respList;
    }

    /**
     * 新增巡逻队伍
     *
     * @param reqVO 巡逻队伍信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPatrolTeam(PatrolTeamSaveReqVO reqVO) {
        PatrolTeam patrolTeam = new PatrolTeam();
        BeanUtils.copyProperties(reqVO, patrolTeam);

        patrolTeam.setCreateBy(SecurityUtils.getUsername());
        patrolTeam.setCreateTime(DateUtils.getNowDate());
        patrolTeam.setDelFlag("0");
        int rows = patrolTeamMapper.insertPatrolTeam(patrolTeam);
        // 新增队伍成员关联
        insertPatrolTeamMember(patrolTeam);
        return rows;
    }

    /**
     * 修改巡逻队伍
     *
     * @param reqVO 巡逻队伍信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePatrolTeam(PatrolTeamSaveReqVO reqVO) {
        PatrolTeam patrolTeam = new PatrolTeam();
        BeanUtils.copyProperties(reqVO, patrolTeam);

        patrolTeam.setUpdateBy(SecurityUtils.getUsername());
        patrolTeam.setUpdateTime(DateUtils.getNowDate());
        // 删除原有队伍成员关联
        patrolTeamMemberMapper.deletePatrolTeamMemberByTeamId(patrolTeam.getTeamId());
        // 新增队伍成员关联
        insertPatrolTeamMember(patrolTeam);
        return patrolTeamMapper.updatePatrolTeam(patrolTeam);
    }

    /**
     * 批量删除巡逻队伍
     *
     * @param teamIds 需要删除的巡逻队伍主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePatrolTeamByTeamIds(Long[] teamIds) {
        // 删除队伍成员关联
        for (Long teamId : teamIds) {
            patrolTeamMemberMapper.deletePatrolTeamMemberByTeamId(teamId);
        }
        return patrolTeamMapper.deletePatrolTeamByTeamIds(teamIds);
    }

    /**
     * 删除巡逻队伍信息
     *
     * @param teamId 巡逻队伍主键
     * @return 结果
     */
    @Override
    public int deletePatrolTeamByTeamId(Long teamId) {
        return patrolTeamMapper.deletePatrolTeamByTeamId(teamId);
    }

    /**
     * 新增队伍成员信息
     */
    public void insertPatrolTeamMember(PatrolTeam patrolTeam) {
        Long[] memberIds = patrolTeam.getMemberIds();
        if (memberIds != null && memberIds.length > 0) {
            List<PatrolTeamMember> list = new ArrayList<>();
            for (Long userId : memberIds) {
                PatrolTeamMember tm = new PatrolTeamMember();
                tm.setTeamId(patrolTeam.getTeamId());
                tm.setUserId(userId);
                list.add(tm);
            }
            patrolTeamMemberMapper.batchPatrolTeamMember(list);
        }
    }

    /**
     * 检查巡逻队伍名称是否在指定部门下重复
     *
     * @param teamName 巡逻队伍名称
     * @param deptId 部门ID
     * @param teamId 巡逻队伍ID（修改时传入，新增时为null）
     * @return 是否重复
     */
    @Override
    public boolean checkTeamNameDuplicate(String teamName, Long deptId, Long teamId) {
        int count = patrolTeamMapper.checkTeamNameDuplicate(teamName, deptId, teamId);
        return count > 0;
    }
}
