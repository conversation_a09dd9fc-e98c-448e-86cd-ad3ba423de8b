package top.sust.patrol.team.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import top.sust.common.annotation.Excel;
import top.sust.common.core.domain.BaseEntity;
import top.sust.common.core.domain.entity.SysUser;

import java.util.List;

/**
 * 巡逻队伍对象 patrol_team
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.team.mapper.PatrolTeamMapper.PatrolTeamResult")
@ApiModel(value = "PatrolTeam", description = "巡逻队伍对象")
public class PatrolTeam extends BaseEntity{
private static final long serialVersionUID=1L;
    /** 巡逻队ID */
    @TableId(value = "team_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "${comment}", name = "teamId")
    private Long teamId;
    /** 巡逻队名称 */
    @Excel(name = "巡逻队名称" )
    @ApiModelProperty(value = "巡逻队名称", name = "teamName")
    private String teamName;
    /** 部门ID */
    @Excel(name = "部门ID" )
    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;
    /** 状态（0正常 1停用） */
    @Excel(name = "状态" , readConverterExp = "0=正常,1=停用" )
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /** 删除标志（0 代表存在 2 代表删除） */
    @ApiModelProperty(value = "状态", name = "delFlag")
    private String delFlag;
    /** 成员数量 */
    @TableField(exist = false)
    @Excel(name = "成员数量")
    @ApiModelProperty(value = "成员数量", name = "memberCount")
    private Integer memberCount;
    /** 队伍成员用户ID列表 */
    @TableField(exist = false)
    @ApiModelProperty(value = "队伍成员用户ID列表", name = "memberIds")
    private Long[] memberIds;
    
    /** 队伍成员列表 */
    @TableField(exist = false)
    @ApiModelProperty(value = "队伍成员列表", name = "members")
    private List<SysUser> members;
    /** 部门名称 */
    @TableField(exist = false)
    @ApiModelProperty(value = "部门名称", name = "deptName")
    private String deptName;

}
