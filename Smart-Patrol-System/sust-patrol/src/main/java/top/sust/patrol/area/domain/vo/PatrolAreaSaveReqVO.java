package top.sust.patrol.area.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class PatrolAreaSaveReqVO {
    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;

    @NotNull(message = "父区域不能为空")
    @ApiModelProperty(value = "父区域ID", name = "areaPid")
    private Long areaPid;

    @NotBlank(message = "区域名称不能为空")
    @ApiModelProperty(value = "区域名称", name = "areaName")
    private String areaName;

    @NotNull(message = "所属部门不能为空")
    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "排序", name = "orderNum")
    private Integer orderNum;
}
