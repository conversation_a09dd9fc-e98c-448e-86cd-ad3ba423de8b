package top.sust.patrol.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

import top.sust.common.exception.ServiceException;
import top.sust.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.sust.common.utils.SecurityUtils;
import top.sust.common.utils.StringUtils;
import top.sust.patrol.template.domain.PatrolItemTemplate;
import top.sust.patrol.template.domain.vo.PatrolItemTemplateListReqVO;
import top.sust.patrol.template.mapper.PatrolItemTemplateMapper;
import top.sust.patrol.template.service.IPatrolItemTemplateService;
@Service
public class PatrolItemTemplateServiceImpl extends ServiceImpl<PatrolItemTemplateMapper, PatrolItemTemplate> implements IPatrolItemTemplateService {
    @Autowired
    private PatrolItemTemplateMapper patrolItemTemplateMapper;

    /**
     * 查询点位检查项模板
     *
     * @param templateId 点位检查项模板主键
     * @return 点位检查项模板
     */
    @Override
    public PatrolItemTemplate selectPatrolItemTemplateByTemplateId(Long templateId) {
        return patrolItemTemplateMapper.selectPatrolItemTemplateByTemplateId(templateId);
    }

    /**
     * 查询点位检查项模板列表
     *
     * @param patrolItemTemplate 点位检查项模板
     * @return 点位检查项模板
     */
    @Override
    public List<PatrolItemTemplate> selectPatrolItemTemplateList(PatrolItemTemplateListReqVO patrolItemTemplate) {
        return patrolItemTemplateMapper.selectPatrolItemTemplateList(patrolItemTemplate);
    }

    /**
     * 新增点位检查项模板
     *
     * @param patrolItemTemplate 点位检查项模板
     * @return 结果
     */
    @Override
    public int insertPatrolItemTemplate(PatrolItemTemplate patrolItemTemplate) {
        // 新增：校验模板名称唯一性
        validateTemplateNameUnique(null, patrolItemTemplate.getTemplateName());

        patrolItemTemplate.setCreateBy(SecurityUtils.getUsername());
        patrolItemTemplate.setCreateTime(DateUtils.getNowDate());
        return patrolItemTemplateMapper.insertPatrolItemTemplate(patrolItemTemplate);
    }
    /**
     * 校验模板名称唯一性
     * @param templateId 模板ID（更新时使用）
     * @param templateName 模板名称
     */
    private void validateTemplateNameUnique(Long templateId, String templateName) {
        // 查询是否存在同名且未删除的模板
        LambdaQueryWrapper<PatrolItemTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatrolItemTemplate::getTemplateName, templateName)
                .eq(PatrolItemTemplate::getDelFlag, "0");

        // 如果是更新操作，排除当前记录
        if (templateId != null) {
            queryWrapper.ne(PatrolItemTemplate::getTemplateId, templateId);
        }

        PatrolItemTemplate template = patrolItemTemplateMapper.selectOne(queryWrapper);
        if (template != null) {
            throw new ServiceException("已存在同名的检查项模板，请更换名称");
        }
    }
    /**
     * 修改点位检查项模板
     *
     * @param patrolItemTemplate 点位检查项模板
     * @return 结果
     */
    @Override
    public int updatePatrolItemTemplate(PatrolItemTemplate patrolItemTemplate) {
        // 新增：校验模板名称唯一性
        if (StringUtils.isNotBlank(patrolItemTemplate.getTemplateName())) {
            validateTemplateNameUnique(patrolItemTemplate.getTemplateId(), patrolItemTemplate.getTemplateName());
        }
        patrolItemTemplate.setUpdateBy(SecurityUtils.getUsername());
        patrolItemTemplate.setUpdateTime(DateUtils.getNowDate());
        return patrolItemTemplateMapper.updatePatrolItemTemplate(patrolItemTemplate);
    }

    /**
     * 批量删除点位检查项模板
     *
     * @param templateIds 需要删除的点位检查项模板主键
     * @return 结果
     */
    @Override
    public int deletePatrolItemTemplateByTemplateIds(Long[] templateIds) {
        return patrolItemTemplateMapper.deletePatrolItemTemplateByTemplateIds(templateIds);
    }


    /**
     * 删除点位检查项模板信息
     *
     * @param templateId 点位检查项模板主键
     * @return 结果
     */
    @Override
    public int deletePatrolItemTemplateByTemplateId(Long templateId) {
        return patrolItemTemplateMapper.deletePatrolItemTemplateByTemplateId(templateId);
    }
}
