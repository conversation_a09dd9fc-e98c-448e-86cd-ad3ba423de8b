package top.sust.patrol.schedule.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巡检计划简单响应VO
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@ApiModel(value = "ScheduleSimpleRespVO", description = "巡检计划简单响应VO")
public class ScheduleSimpleRespVO {

    @ApiModelProperty(value = "计划ID", name = "scheduleId")
    private Long scheduleId;

    @ApiModelProperty(value = "计划名称", name = "scheduleName")
    private String scheduleName;

    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;

    @ApiModelProperty(value = "路线名称", name = "routeName")
    private String routeName;

    @ApiModelProperty(value = "队伍ID", name = "teamId")
    private Long teamId;

    @ApiModelProperty(value = "队伍名称", name = "teamName")
    private String teamName;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;
}
