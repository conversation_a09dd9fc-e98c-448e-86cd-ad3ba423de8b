package top.sust.patrol.team.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import top.sust.patrol.team.domain.PatrolTeamMember;

import java.util.List;

/**
 * 巡逻队成员关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Mapper
public interface PatrolTeamMemberMapper extends BaseMapper<PatrolTeamMember> {
    
    /**
     * 根据队伍ID删除成员关联
     *
     * @param teamId 队伍ID
     * @return 结果
     */
    public int deletePatrolTeamMemberByTeamId(Long teamId);
    
    /**
     * 批量新增队伍成员关联
     *
     * @param patrolTeamMemberList 队伍成员关联列表
     * @return 结果
     */
    public int batchPatrolTeamMember(List<PatrolTeamMember> patrolTeamMemberList);
    
    /**
     * 根据队伍ID查询成员用户ID列表
     *
     * @param teamId 队伍ID
     * @return 用户ID列表
     */
    public List<Long> selectUserIdsByTeamId(Long teamId);
}