package top.sust.patrol.route.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import top.sust.common.core.domain.BaseEntity;

/**
 * 巡检路线对象 patrol_route
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.route.mapper.PatrolRouteMapper.PatrolRouteResult")
@ApiModel(value = "PatrolRoute", description = "巡检路线对象")
public class PatrolRoute extends BaseEntity{
private static final long serialVersionUID=1L;
    /** 路线ID */
    @TableId(value = "route_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "${comment}", name = "routeId")
    private Long routeId;
    /** 路线名称 */
    @Excel(name = "路线名称" )
    @ApiModelProperty(value = "路线名称", name = "routeName")
    private String routeName;
    /** 所属部门 */
    @Excel(name = "所属部门" )
    @ApiModelProperty(value = "所属部门", name = "deptId")
    private Long deptId;
    /** 状态 */
    @Excel(name = "状态" )
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /** 逻辑删除 */
    @ApiModelProperty(value = "状态", name = "delFlag")
    private String delFlag;
}
