package top.sust.patrol.area.service;

import java.util.List;

import top.sust.common.core.domain.TreeSelect;
import top.sust.patrol.area.domain.PatrolArea;
import com.baomidou.mybatisplus.extension.service.IService;
import top.sust.patrol.area.domain.vo.PatrolAreaListReqVO;
import top.sust.patrol.area.domain.vo.PatrolAreaSaveReqVO;

/**
 * 巡检区域Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IPatrolAreaService extends IService<PatrolArea> {
    /**
     * 查询巡检区域
     *
     * @param areaId 巡检区域主键
     * @return 巡检区域
     */
    public PatrolArea selectPatrolAreaByAreaId(Long areaId);

    public List<TreeSelect> selectAreaTreeList(PatrolAreaListReqVO patrolArea);

    public boolean hasChildByAreaId(Long AreaId);

    /**
     * 查询巡检区域列表
     *
     * @param patrolArea 巡检区域
     * @return 巡检区域集合
     */
    public List<PatrolArea> selectPatrolAreaList(PatrolAreaListReqVO patrolArea);

    /**
     * 新增巡检区域
     *
     * @param patrolArea 巡检区域
     * @return 结果
     */
    public int insertPatrolArea(PatrolAreaSaveReqVO patrolArea);

    /**
     * 修改巡检区域
     *
     * @param patrolArea 巡检区域
     * @return 结果
     */
    public int updatePatrolArea(PatrolAreaSaveReqVO patrolArea);

    /**
     * 批量删除巡检区域
     *
     * @param areaIds 需要删除的巡检区域主键集合
     * @return 结果
     */
    public int deletePatrolAreaByAreaIds(Long[] areaIds);

    /**
     * 删除巡检区域信息
     *
     * @param areaId 巡检区域主键
     * @return 结果
     */
    public int deletePatrolAreaByAreaId(Long areaId);
}
