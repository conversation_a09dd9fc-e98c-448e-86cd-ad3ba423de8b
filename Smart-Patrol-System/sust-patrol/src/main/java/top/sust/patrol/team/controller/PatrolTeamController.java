package top.sust.patrol.team.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.entity.SysUser;
import top.sust.common.core.page.TableDataInfo;
import top.sust.common.enums.BusinessType;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.patrol.team.domain.vo.PatrolTeamDetailRespVO;
import top.sust.patrol.team.domain.vo.PatrolTeamListReqVO;
import top.sust.patrol.team.domain.vo.PatrolTeamListRespVO;
import top.sust.patrol.team.domain.vo.PatrolTeamSaveReqVO;
import top.sust.patrol.team.service.IPatrolTeamService;
import top.sust.system.service.ISysUserService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 巡逻队伍接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Api(tags = "巡逻队伍管理接口")
@RestController
@RequestMapping("/patrol/team")
public class PatrolTeamController extends BaseController
{
    @Autowired
    private IPatrolTeamService patrolTeamService;
    @Autowired
    private ISysUserService userService;


    /**
     * 查询巡逻队伍列表
     */
    @ApiOperation(value = "查询巡逻队伍列表")
    @PreAuthorize("@ss.hasPermi('patrol:team:list')")
    @GetMapping("/list")
    public TableDataInfo list(PatrolTeamListReqVO reqVO)
    {
        startPage();
        List<PatrolTeamListRespVO> list = patrolTeamService.selectPatrolTeamList(reqVO);
        return getDataTable(list);
    }

    /**
     * 导出巡逻队伍列表
     */
    @ApiOperation(value = "导出巡逻队伍列表")
    @PreAuthorize("@ss.hasPermi('patrol:team:export')")
    @Log(title = "巡逻队伍", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolTeamListReqVO reqVO)
    {
        List<PatrolTeamListRespVO> list = patrolTeamService.selectPatrolTeamList(reqVO);
        ExcelUtil<PatrolTeamListRespVO> util = new ExcelUtil<PatrolTeamListRespVO>(PatrolTeamListRespVO.class);
        util.exportExcel(response, list, "巡逻队伍数据");
    }

    /**
     * 根据队伍ID获取详细信息（包含成员）
     */
    @ApiOperation(value = "根据队伍ID获取详细信息")
    @PreAuthorize("@ss.hasPermi('patrol:team:query')")
    @GetMapping(value = "/{teamId}")
    public AjaxResult getInfo(@PathVariable("teamId") Long teamId) {
        PatrolTeamDetailRespVO respVO = patrolTeamService.selectPatrolTeamByTeamId(teamId);
        return success(respVO);
    }

    /**
     * 新增巡逻队伍
     */
    @ApiOperation(value = "新增巡逻队伍")
    @PreAuthorize("@ss.hasPermi('patrol:team:add')")
    @Log(title = "巡逻队伍", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatrolTeamSaveReqVO reqVO)
    {
        return toAjax(patrolTeamService.insertPatrolTeam(reqVO));
    }

    /**
     * 修改巡逻队伍
     */
    @ApiOperation(value = "修改巡逻队伍")
    @PreAuthorize("@ss.hasPermi('patrol:team:edit')")
    @Log(title = "巡逻队伍", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatrolTeamSaveReqVO reqVO)
    {
        return toAjax(patrolTeamService.updatePatrolTeam(reqVO));
    }

    /**
     * 删除巡逻队伍
     */
    @ApiOperation(value = "删除巡逻队伍")
    @PreAuthorize("@ss.hasPermi('patrol:team:remove')")
    @Log(title = "巡逻队伍", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teamIds}")
    public AjaxResult remove(@PathVariable Long[] teamIds)
    {
        return toAjax(patrolTeamService.deletePatrolTeamByTeamIds(teamIds));
    }

    /**
     * 获取可选择的用户列表
     */
    @PreAuthorize("@ss.hasPermi('patrol:team:list')")
    @GetMapping("/users")
    public TableDataInfo getUserList(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    /**
     * 检查巡逻队伍名称是否重复
     */
    @ApiOperation(value = "检查巡逻队伍名称是否重复")
    @PreAuthorize("@ss.hasPermi('patrol:team:list')")
    @GetMapping("/checkTeamName")
    public AjaxResult checkTeamName(String teamName, Long deptId, Long teamId) {
        boolean isDuplicate = patrolTeamService.checkTeamNameDuplicate(teamName, deptId, teamId);
        return success(isDuplicate);
    }
}
