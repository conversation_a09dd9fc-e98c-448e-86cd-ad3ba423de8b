package top.sust.patrol.route.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import top.sust.common.annotation.Excel;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检路线响应VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@ApiModel(value = "PatrolRouteRespVO", description = "巡检路线响应对象")
public class PatrolRouteRespVO {

    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;

    @Excel(name = "路线名称")
    @ApiModelProperty(value = "路线名称", name = "routeName")
    private String routeName;

    @ApiModelProperty(value = "所属部门ID", name = "deptId")
    private Long deptId;

    @Excel(name = "所属部门")
    @ApiModelProperty(value = "所属部门名称", name = "deptName")
    private String deptName;

    @Excel(name = "状态")
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "创建时间", name = "createTime")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者", name = "updateBy")
    private String updateBy;

    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "关联的点位信息", name = "patrolPoints")
    private List<PatrolPointInfo> patrolPoints;

    /**
     * 点位信息内部类
     */
    @Data
    @ApiModel(value = "PatrolPointInfo", description = "点位信息")
    public static class PatrolPointInfo {
        @ApiModelProperty(value = "点位ID", name = "pointId")
        private Long pointId;

        @ApiModelProperty(value = "点位名称", name = "pointName")
        private String pointName;

        @ApiModelProperty(value = "点位在路线中的顺序", name = "pointOrder")
        private Integer pointOrder;

        @ApiModelProperty(value = "点位状态", name = "status")
        private String status;
    }
}