package top.sust.patrol.schedule.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import top.sust.common.annotation.Excel;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 巡检计划详细响应VO
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@ApiModel(value = "ScheduleRespVO", description = "巡检计划详细响应VO")
public class ScheduleRespVO {

    @ApiModelProperty(value = "计划ID", name = "scheduleId")
    private Long scheduleId;

    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;

    @Excel(name = "路线名称")
    @ApiModelProperty(value = "路线名称", name = "routeName")
    private String routeName;

    @Excel(name = "计划名称")
    @ApiModelProperty(value = "计划名称", name = "scheduleName")
    private String scheduleName;

    @ApiModelProperty(value = "队伍ID", name = "teamId")
    private Long teamId;

    @Excel(name = "队伍名称")
    @ApiModelProperty(value = "队伍名称", name = "teamName")
    private String teamName;

    @Excel(name = "每日开始时间", dateFormat = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty(value = "每日开始时间", name = "scheduledStartTime")
    private LocalTime scheduledStartTime;

    @Excel(name = "时长（分钟）")
    @ApiModelProperty(value = "时长（单位：分钟）", name = "duration")
    private Long duration;

    @Excel(name = "周期", readConverterExp = "1=周一,2=周二,3=周三,4=周四,5=周五,6=周六,7=周日")
    @ApiModelProperty(value = "周期（指定一周内哪几天执行，例如1,2,3,4,5）", name = "period")
    private String period;

    @Excel(name = "状态", readConverterExp = "0=启用,1=停用")
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @Excel(name = "创建者")
    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者", name = "updateBy")
    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private LocalDateTime updateTime;
}
