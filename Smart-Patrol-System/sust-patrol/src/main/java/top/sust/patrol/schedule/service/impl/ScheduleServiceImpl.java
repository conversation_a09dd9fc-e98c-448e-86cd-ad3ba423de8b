package top.sust.patrol.schedule.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import top.sust.common.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.sust.common.utils.SecurityUtils;
import top.sust.patrol.schedule.mapper.ScheduleMapper;
import top.sust.patrol.schedule.domain.Schedule;
import top.sust.patrol.schedule.domain.vo.ScheduleListReqVO;
import top.sust.patrol.schedule.domain.vo.ScheduleRespVO;
import top.sust.patrol.schedule.domain.vo.ScheduleSaveReqVO;
import top.sust.patrol.schedule.domain.vo.ScheduleSimpleRespVO;
import top.sust.patrol.schedule.service.IScheduleService;

/**
 * 计划表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class ScheduleServiceImpl extends ServiceImpl<ScheduleMapper, Schedule> implements IScheduleService {
    @Autowired
    private ScheduleMapper scheduleMapper;

    /**
     * 查询计划表
     *
     * @param scheduleId 计划表主键
     * @return 计划表
     */
    @Override
    public Schedule selectScheduleByScheduleId(Long scheduleId) {
        return scheduleMapper.selectScheduleByScheduleId(scheduleId);
    }

    /**
     * 查询计划表列表
     *
     * @param schedule 计划表
     * @return 计划表
     */
    @Override
    public List<Schedule> selectScheduleList(Schedule schedule) {
        return scheduleMapper.selectScheduleList(schedule);
    }

    /**
     * 新增计划表
     *
     * @param schedule 计划表
     * @return 结果
     */
    @Override
    public int insertSchedule(Schedule schedule) {
        schedule.setCreateBy(SecurityUtils.getUsername());
        schedule.setCreateTime(DateUtils.getNowDate());
        return scheduleMapper.insertSchedule(schedule);
    }

    /**
     * 修改计划表
     *
     * @param schedule 计划表
     * @return 结果
     */
    @Override
    public int updateSchedule(Schedule schedule) {
        schedule.setCreateBy(SecurityUtils.getUsername());
        schedule.setUpdateTime(DateUtils.getNowDate());
        return scheduleMapper.updateSchedule(schedule);
    }

    /**
     * 批量删除计划表
     *
     * @param scheduleIds 需要删除的计划表主键
     * @return 结果
     */
    @Override
    public int deleteScheduleByScheduleIds(Long[] scheduleIds) {
        return scheduleMapper.deleteScheduleByScheduleIds(scheduleIds);
    }

    /**
     * 删除计划表信息
     *
     * @param scheduleId 计划表主键
     * @return 结果
     */
    @Override
    public int deleteScheduleByScheduleId(Long scheduleId) {
        return scheduleMapper.deleteScheduleByScheduleId(scheduleId);
    }

    /**
     * 查询巡检计划列表（带关联信息）
     *
     * @param reqVO 查询条件
     * @return 巡检计划列表
     */
    @Override
    public List<ScheduleRespVO> selectScheduleRespList(ScheduleListReqVO reqVO) {
        return scheduleMapper.selectScheduleRespList(reqVO);
    }

    /**
     * 查询巡检计划详情（带关联信息）
     *
     * @param scheduleId 计划ID
     * @return 巡检计划详情
     */
    @Override
    public ScheduleRespVO selectScheduleRespByScheduleId(Long scheduleId) {
        return scheduleMapper.selectScheduleRespByScheduleId(scheduleId);
    }

    /**
     * 查询巡检计划简单列表
     *
     * @return 巡检计划简单列表
     */
    @Override
    public List<ScheduleSimpleRespVO> selectScheduleSimpleList() {
        return scheduleMapper.selectScheduleSimpleList();
    }

    /**
     * 新增巡检计划（VO方法）
     *
     * @param reqVO 巡检计划信息
     * @return 结果
     */
    @Override
    public int insertScheduleByVO(ScheduleSaveReqVO reqVO) {
        Schedule schedule = new Schedule();
        BeanUtils.copyProperties(reqVO, schedule);
        schedule.setCreateBy(SecurityUtils.getUsername());
        schedule.setCreateTime(DateUtils.getNowDate());
        schedule.setDelFlag("0");
        return scheduleMapper.insertSchedule(schedule);
    }

    /**
     * 修改巡检计划（VO方法）
     *
     * @param reqVO 巡检计划信息
     * @return 结果
     */
    @Override
    public int updateScheduleByVO(ScheduleSaveReqVO reqVO) {
        Schedule schedule = new Schedule();
        BeanUtils.copyProperties(reqVO, schedule);
        schedule.setUpdateBy(SecurityUtils.getUsername());
        schedule.setUpdateTime(DateUtils.getNowDate());
        return scheduleMapper.updateSchedule(schedule);
    }

    /**
     * 查询启用的巡检计划（用于定时任务）
     *
     * @param dayOfWeek 星期几（1-7）
     * @return 启用的巡检计划列表
     */
    @Override
    public List<Schedule> selectEnabledSchedulesByDayOfWeek(Integer dayOfWeek) {
        return scheduleMapper.selectEnabledSchedulesByDayOfWeek(dayOfWeek);
    }
}
