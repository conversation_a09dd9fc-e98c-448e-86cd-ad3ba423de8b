package top.sust.patrol.area.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class PatrolAreaRespVO {
    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;

    @ApiModelProperty(value = "父区域ID", name = "areaPid")
    private Long areaPid;

    @ApiModelProperty(value = "区域名称", name = "areaName")
    private String areaName;

    @ApiModelProperty(value = "部门ID", name = "deptId")
    private Long deptId;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Date createTime;

    @ApiModelProperty(value = "排序", name = "orderNum")
    private Integer orderNum;

    private String deptName;
}
