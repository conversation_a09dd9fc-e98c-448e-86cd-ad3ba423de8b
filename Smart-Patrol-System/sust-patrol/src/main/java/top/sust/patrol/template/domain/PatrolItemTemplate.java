package top.sust.patrol.template.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import top.sust.common.core.domain.BaseEntity;

/**
 * itemTemplate对象 patrol_item_template
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.template.mapper.PatrolItemTemplateMapper.PatrolItemTemplateResult")
@ApiModel(value = "PatrolItemTemplate", description = "itemTemplate对象")
public class PatrolItemTemplate extends BaseEntity{
private static final long serialVersionUID=1L;
    /** 检查项模板ID */
    @TableId(value = "template_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "${comment}", name = "templateId")
    private Long templateId;
    /** 检查项模板名称 */
    @Excel(name = "检查项模板名称" )
    @ApiModelProperty(value = "检查项模板名称", name = "templateName")
    private String templateName;
    /** 描述 */
    @Excel(name = "描述" )
    @ApiModelProperty(value = "描述", name = "description")
    private String description;

    /** 检查项类型（新增字段）*/
    @Excel(name = "检查项类型")
    @ApiModelProperty(value = "检查项类型", name = "type")
    private String type;
    /** 逻辑删除 */
    @ApiModelProperty(value = "描述", name = "delFlag")
    private String delFlag;
}
