package top.sust.patrol.beacon.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import top.sust.common.core.domain.BaseEntity;

/**
 * 信标管理对象 patrol_beacon
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "top.sust.patrol.beacon.mapper.PatrolBeaconMapper.PatrolBeaconResult")
@ApiModel(value = "PatrolBeacon", description = "信标管理对象")
public class PatrolBeacon extends BaseEntity{
private static final long serialVersionUID=1L;
    /** 信标ID */
    @TableId(value = "beacon_id" , type = IdType.AUTO)
    @ApiModelProperty(value = "${comment}", name = "beaconId")
    private Long beaconId;
    /** 逻辑编号 */
    @Excel(name = "信标编号" )
    @ApiModelProperty(value = "逻辑编号", name = "beaconName")
    private String beaconName;
    /** 信标mac */
    @Excel(name = "信标mac" )
    @ApiModelProperty(value = "信标mac", name = "beaconMac")
    private String beaconMac;
    /** UUID */
    @Excel(name = "UUID" )
    @ApiModelProperty(value = "UUID", name = "beaconUuid")
    private String beaconUuid;
    /** Major UUID */
    @Excel(name = "Major UUID" )
    @ApiModelProperty(value = "Major UUID", name = "beaconMajor")
    private String beaconMajor;
    /** Minor UUID */
    @Excel(name = "Minor UUID" )
    @ApiModelProperty(value = "Minor UUID", name = "beaconMinor")
    private String beaconMinor;
    /** 状态 */
    @Excel(name = "状态" )
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /** 逻辑删除 */
    @ApiModelProperty(value = "逻辑删除", name = "delFlag")
    private String delFlag;
}
