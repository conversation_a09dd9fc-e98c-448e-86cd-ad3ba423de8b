package top.sust.patrol.point.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.sust.common.annotation.Log;
import top.sust.common.core.controller.BaseController;
import top.sust.common.core.domain.AjaxResult;
import top.sust.common.core.domain.R;
import top.sust.common.enums.BusinessType;
import top.sust.common.utils.bean.BeanUtils;
import top.sust.patrol.point.domain.PatrolPoint;
import top.sust.patrol.point.domain.vo.PatrolPointListReqVO;
import top.sust.patrol.point.domain.vo.PatrolPointRespVO;
import top.sust.patrol.point.domain.vo.PatrolPointSaveReqVO;

import top.sust.patrol.point.service.IPatrolPointService;
import top.sust.common.utils.poi.ExcelUtil;
import top.sust.common.core.page.TableDataInfo;

/**
 * 巡检点位接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Api(tags = "巡检点位管理接口")
@RestController
@RequestMapping("/patrol/point")
public class PatrolPointController extends BaseController
{
    @Autowired
    private IPatrolPointService patrolPointService;

    /**
     * 查询巡检点位列表
     */
    @ApiOperation(value = "查询巡检点位列表")
    @PreAuthorize("@ss.hasPermi('patrol:point:list')")
    @GetMapping("/list")
    public TableDataInfo list(PatrolPointListReqVO patrolPointListReqVO)
    {
        startPage();
        List<PatrolPoint> list = patrolPointService.selectPatrolPointList(patrolPointListReqVO);
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(BeanUtils.toBean(list, PatrolPointRespVO.class));
        return dataTable;
    }

    /**
     * 导出巡检点位列表
     */
    @ApiOperation(value = "导出巡检点位列表")
    @PreAuthorize("@ss.hasPermi('patrol:point:export')")
    @Log(title = "巡检点位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolPointListReqVO patrolPointListReqVO)
    {
        List<PatrolPoint> list = patrolPointService.selectPatrolPointList(patrolPointListReqVO);
        ExcelUtil<PatrolPoint> util = new ExcelUtil<PatrolPoint>(PatrolPoint.class);
        util.exportExcel(response, list, "巡检点位数据");
    }

    /**
     * 获取巡检点位详细信息
     */
    @ApiOperation(value = "获取巡检点位详细信息")
    @PreAuthorize("@ss.hasPermi('patrol:point:query')")
    @GetMapping(value = "/{pointId}")
    public R<PatrolPointRespVO> getInfo(@PathVariable("pointId") Long pointId)
    {
        PatrolPoint patrolPoint = patrolPointService.selectPatrolPointByPointId(pointId);
        return R.ok(BeanUtils.toBean(patrolPoint, PatrolPointRespVO.class));
    }

    /**
     * 新增巡检点位
     */
    @ApiOperation(value = "新增巡检点位")
    @PreAuthorize("@ss.hasPermi('patrol:point:add')")
    @Log(title = "巡检点位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatrolPointSaveReqVO patrolPointSaveReqVO)
    {
        return toAjax(patrolPointService.insertPatrolPoint(patrolPointSaveReqVO));
    }

    /**
     * 修改巡检点位
     */
    @ApiOperation(value = "修改巡检点位")
    @PreAuthorize("@ss.hasPermi('patrol:point:edit')")
    @Log(title = "巡检点位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatrolPointSaveReqVO patrolPointSaveReqVO)
    {
        return toAjax(patrolPointService.updatePatrolPoint(patrolPointSaveReqVO));
    }

    /**
     * 删除巡检点位
     */
    @ApiOperation(value = "删除巡检点位")
    @PreAuthorize("@ss.hasPermi('patrol:point:remove')")
    @Log(title = "巡检点位", businessType = BusinessType.DELETE)
	@DeleteMapping("/{pointIds}")
    public AjaxResult remove(@PathVariable Long[] pointIds)
    {
        return toAjax(patrolPointService.deletePatrolPointByPointIds(pointIds));
    }

}
