package top.sust.patrol.schedule.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;

/**
 * 巡检计划保存请求VO
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@ApiModel(value = "ScheduleSaveReqVO", description = "巡检计划保存请求VO")
public class ScheduleSaveReqVO {

    @ApiModelProperty(value = "计划ID", name = "scheduleId")
    private Long scheduleId;

    @NotNull(message = "路线ID不能为空")
    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;

    @NotBlank(message = "计划名称不能为空")
    @ApiModelProperty(value = "计划名称", name = "scheduleName")
    private String scheduleName;

    @NotNull(message = "队伍ID不能为空")
    @ApiModelProperty(value = "队伍ID", name = "teamId")
    private Long teamId;

    @NotNull(message = "每日开始时间不能为空")
    @ApiModelProperty(value = "每日开始时间", name = "scheduledStartTime")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime scheduledStartTime;

    @NotNull(message = "时长不能为空")
    @ApiModelProperty(value = "时长（单位：分钟）", name = "duration")
    private Long duration;

    @NotBlank(message = "周期不能为空")
    @ApiModelProperty(value = "周期（指定一周内哪几天执行，例如1,2,3,4,5）", name = "period")
    private String period;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;
}
