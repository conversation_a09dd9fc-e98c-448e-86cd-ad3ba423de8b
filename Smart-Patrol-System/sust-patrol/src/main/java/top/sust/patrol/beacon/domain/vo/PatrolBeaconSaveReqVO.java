package top.sust.patrol.beacon.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@ApiModel(description = "保存巡逻信标请求参数")
@NoArgsConstructor
public class PatrolBeaconSaveReqVO {

    @ApiModelProperty(value = "${comment}", name = "beaconId")
    private Long beaconId;

    @NotBlank(message = "信标编号不能为空")
    @ApiModelProperty(value = "逻辑编号", name = "beaconName")
    private String beaconName;

    @NotBlank(message = "信标mac不能为空")
    @ApiModelProperty(value = "信标mac", name = "beaconMac")
    private String beaconMac;

    @ApiModelProperty(value = "UUID", name = "beaconUuid")
    private String beaconUuid;

    @ApiModelProperty(value = "Major UUID", name = "beaconMajor")
    private String beaconMajor;

    @ApiModelProperty(value = "Minor UUID", name = "beaconMinor")
    private String beaconMinor;

    @NotBlank(message = "状态不能为空")
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @NotBlank(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除", name = "delFlag")
    private String delFlag;

}
