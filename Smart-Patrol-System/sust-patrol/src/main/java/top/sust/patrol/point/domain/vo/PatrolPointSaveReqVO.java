package top.sust.patrol.point.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import top.sust.patrol.point.domain.PatrolPointItem;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class PatrolPointSaveReqVO {
    
    @ApiModelProperty(value = "点位ID", name = "pointId")
    private Long pointId;
    
    @NotBlank(message = "点位名称不能为空")
    @ApiModelProperty(value = "点位名称", name = "pointName")
    private String pointName;
    
    @NotNull(message = "区域ID不能为空")
    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Long areaId;
    
    @NotNull(message = "信标ID不能为空")
    @ApiModelProperty(value = "信标ID", name = "beaconId")
    private Long beaconId;

    @NotNull(message = "经度不能为空")
    @ApiModelProperty(value = "经度", name = "longtitude")
    private BigDecimal longtitude;

    @NotNull(message = "纬度不能为空")
    @ApiModelProperty(value = "纬度", name = "latitude")
    private BigDecimal latitude;

    @NotNull(message = "X坐标不能为空")
    @ApiModelProperty(value = "X坐标", name = "locationX")
    private BigDecimal locationX;

    @NotNull(message = "Y坐标不能为空")
    @ApiModelProperty(value = "Y坐标", name = "locationY")
    private BigDecimal locationY;

    @NotNull(message = "图层不能为空")
    @ApiModelProperty(value = "图层", name = "locationLayer")
    private Long locationLayer;

    @NotBlank(message = "状态不能为空")
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    
    @ApiModelProperty(value = "巡检点位项目列表", name = "patrolPointItemList")
    private List<PatrolPointItem> patrolPointItemList;
}
