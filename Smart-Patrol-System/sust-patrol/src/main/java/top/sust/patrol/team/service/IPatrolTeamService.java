package top.sust.patrol.team.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.sust.patrol.team.domain.PatrolTeam;
import top.sust.patrol.team.domain.vo.PatrolTeamDetailRespVO;
import top.sust.patrol.team.domain.vo.PatrolTeamListReqVO;
import top.sust.patrol.team.domain.vo.PatrolTeamListRespVO;
import top.sust.patrol.team.domain.vo.PatrolTeamSaveReqVO;

import java.util.List;

/**
 * 巡逻队伍Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IPatrolTeamService extends IService<PatrolTeam> {
    /**
     * 查询巡逻队伍详情
     *
     * @param teamId 巡逻队伍主键
     * @return 巡逻队伍详情
     */
    public PatrolTeamDetailRespVO selectPatrolTeamByTeamId(Long teamId);

    /**
     * 查询巡逻队伍列表
     *
     * @param reqVO 查询条件
     * @return 巡逻队伍集合
     */
    public List<PatrolTeamListRespVO> selectPatrolTeamList(PatrolTeamListReqVO reqVO);

    /**
     * 新增巡逻队伍
     *
     * @param reqVO 巡逻队伍信息
     * @return 结果
     */
    public int insertPatrolTeam(PatrolTeamSaveReqVO reqVO);

    /**
     * 修改巡逻队伍
     *
     * @param reqVO 巡逻队伍信息
     * @return 结果
     */
    public int updatePatrolTeam(PatrolTeamSaveReqVO reqVO);

    /**
     * 批量删除巡逻队伍
     *
     * @param teamIds 需要删除的巡逻队伍主键集合
     * @return 结果
     */
    public int deletePatrolTeamByTeamIds(Long[] teamIds);

    /**
     * 删除巡逻队伍信息
     *
     * @param teamId 巡逻队伍主键
     * @return 结果
     */
    public int deletePatrolTeamByTeamId(Long teamId);

    /**
     * 检查巡逻队伍名称是否在指定部门下重复
     *
     * @param teamName 巡逻队伍名称
     * @param deptId 部门ID
     * @param teamId 巡逻队伍ID（修改时传入，新增时为null）
     * @return 是否重复
     */
    public boolean checkTeamNameDuplicate(String teamName, Long deptId, Long teamId);
}
