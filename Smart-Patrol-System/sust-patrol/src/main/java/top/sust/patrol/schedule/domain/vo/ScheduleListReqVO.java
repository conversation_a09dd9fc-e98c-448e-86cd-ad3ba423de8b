package top.sust.patrol.schedule.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import top.sust.common.core.page.PageDomain;

import java.time.LocalDateTime;

/**
 * 巡检计划查询请求VO
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ScheduleListReqVO", description = "巡检计划查询请求VO")
public class ScheduleListReqVO extends PageDomain {

    @ApiModelProperty(value = "计划名称", name = "scheduleName")
    private String scheduleName;

    @ApiModelProperty(value = "路线ID", name = "routeId")
    private Long routeId;

    @ApiModelProperty(value = "路线名称", name = "routeName")
    private String routeName;

    @ApiModelProperty(value = "队伍ID", name = "teamId")
    private Long teamId;

    @ApiModelProperty(value = "队伍名称", name = "teamName")
    private String teamName;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "周期", name = "period")
    private String period;

    @ApiModelProperty(value = "创建时间", name = "createTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] createTime;
}
