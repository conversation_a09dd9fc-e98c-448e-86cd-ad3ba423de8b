package top.sust.patrol.point.service;

import java.util.List;
import top.sust.patrol.point.domain.PatrolPoint;
import top.sust.patrol.point.domain.vo.PatrolPointListReqVO;
import top.sust.patrol.point.domain.vo.PatrolPointSaveReqVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 巡检点位Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IPatrolPointService extends IService<PatrolPoint> {
    /**
     * 查询巡检点位
     *
     * @param pointId 巡检点位主键
     * @return 巡检点位
     */
    public PatrolPoint selectPatrolPointByPointId(Long pointId);

    /**
     * 查询巡检点位列表
     *
     * @param patrolPoint 巡检点位查询条件
     * @return 巡检点位集合
     */
    public List<PatrolPoint> selectPatrolPointList(PatrolPointListReqVO patrolPoint);

    /**
     * 新增巡检点位
     *
     * @param patrolPointSaveReqVO 巡检点位保存请求
     * @return 结果
     */
    public int insertPatrolPoint(PatrolPointSaveReqVO patrolPointSaveReqVO);

    /**
     * 修改巡检点位
     *
     * @param patrolPointSaveReqVO 巡检点位保存请求
     * @return 结果
     */
    public int updatePatrolPoint(PatrolPointSaveReqVO patrolPointSaveReqVO);

    /**
     * 批量删除巡检点位
     *
     * @param pointIds 需要删除的巡检点位主键集合
     * @return 结果
     */
    public int deletePatrolPointByPointIds(Long[] pointIds);

    /**
     * 检查点位是否被巡检路线使用
     *
     * @param pointIds 点位ID数组
     * @return 使用该点位的路线数量
     */
    public int countRoutesUsingPoints(Long[] pointIds);
}
