package top.sust.patrol.team.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 巡逻队成员关联对象 patrol_team_member
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("patrol_team_member")
@ApiModel(value = "PatrolTeamMember", description = "巡逻队成员关联对象")
public class PatrolTeamMember {
    
    /** 队伍ID */
    @ApiModelProperty(value = "队伍ID", name = "teamId")
    private Long teamId;
    
    /** 人员ID */
    @ApiModelProperty(value = "人员ID", name = "userId")
    private Long userId;

    private String deptName;
}