package ${packageName}.domain;

    #foreach ($import in $subImportList)
    import ${import};
    #end
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import top.sust.common.annotation.Excel;
import lombok.*;
import lombok.EqualsAndHashCode;
import top.sust.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${subTable.functionName}对象 ${subTableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "${packageName}.mapper.${ClassName}Mapper.${ClassName}Result")
@ApiModel(value = "${ClassName}", description = "${functionName}对象")
public class ${subClassName} extends BaseEntity {
private static final long serialVersionUID = 1L;

#foreach ($column in $subTable.columns)
    #if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            #elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
            #else
    @Excel(name = "${comment}")
            #end
        #end
    @ApiModelProperty(value = "${comment}", name = "$column.javaField")
    private $column.javaType $column.javaField;

    #end
#end

}
