package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import top.sust.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#if($table.crud || $table.sub)
import top.sust.common.core.domain.BaseEntity;
#elseif($table.tree)
import top.sust.common.core.domain.TreeEntity;
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity")
#end
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(resultMap = "${packageName}.mapper.${ClassName}Mapper.${ClassName}Result")
@ApiModel(value = "${ClassName}", description = "${functionName}对象")
public class ${ClassName} extends ${Entity}{
private static final long serialVersionUID=1L;
#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
    @Excel(name = "${comment}" , readConverterExp = "$column.readConverterExp()" )
            #elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "${comment}" , width = 30, dateFormat = "yyyy-MM-dd" )
            #else
    @Excel(name = "${comment}" )
            #end
        #end
        #if($column.isPk == 1)
    @TableId(value = "$column.columnName" , type = IdType.AUTO)
        #end
    @ApiModelProperty(value = "${comment}", name = "$column.javaField")
    private $column.javaType $column.javaField;
    #end
#end
#if($table.sub)
/** $table.subTable.functionName信息 */
    private List<${subClassName}> ${subclassName}List;
#end
}
