package top.sust.quartz.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.sust.common.utils.DateUtils;
import top.sust.common.utils.StringUtils;
import top.sust.patrol.schedule.domain.Schedule;
import top.sust.patrol.schedule.service.IScheduleService;
import top.sust.patrol.task.domain.Task;
import top.sust.patrol.task.service.ITaskService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 巡检计划定时任务
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Component("patrolScheduleTask")
public class PatrolScheduleTask {
    
    private static final Logger log = LoggerFactory.getLogger(PatrolScheduleTask.class);
    
    @Autowired
    private IScheduleService scheduleService;
    
    @Autowired
    private ITaskService taskService;
    
    /**
     * 每天凌晨生成巡检任务
     */
    public void generateDailyTasks() {
        log.info("开始执行巡检任务生成定时任务");
        
        try {
            // 获取当前日期是星期几（1=周一，7=周日）
            int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();
            log.info("当前日期是星期{}", dayOfWeek);
            
            // 查询启用且包含当前日期的计划
            List<Schedule> enabledSchedules = scheduleService.selectEnabledSchedulesByDayOfWeek(dayOfWeek);
            log.info("找到{}个符合条件的巡检计划", enabledSchedules.size());
            
            int successCount = 0;
            int failCount = 0;
            
            for (Schedule schedule : enabledSchedules) {
                try {
                    // 生成任务
                    Task task = createTaskFromSchedule(schedule);
                    int result = taskService.insertTask(task);
                    
                    if (result > 0) {
                        successCount++;
                        log.debug("成功生成任务：计划ID={}, 任务名称={}", schedule.getScheduleId(), task.getTaskName());
                    } else {
                        failCount++;
                        log.warn("生成任务失败：计划ID={}", schedule.getScheduleId());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("生成任务异常：计划ID={}, 错误信息={}", schedule.getScheduleId(), e.getMessage(), e);
                }
            }
            
            log.info("巡检任务生成完成：成功{}个，失败{}个", successCount, failCount);
            
        } catch (Exception e) {
            log.error("执行巡检任务生成定时任务异常", e);
        }
    }
    
    /**
     * 根据计划创建任务
     */
    private Task createTaskFromSchedule(Schedule schedule) {
        Task task = new Task();
        
        // 基本信息
        task.setScheduleId(schedule.getScheduleId());
        task.setRouteId(schedule.getRouteId());
        task.setTeamId(schedule.getTeamId());
        task.setTaskType("1"); // 周期性任务
        task.setStatus("0"); // 未完成
        
        // 任务名称：计划名称 + 日期
        String taskName = schedule.getScheduleName() + "_" + DateUtils.dateTimeNow("yyyyMMdd");
        task.setTaskName(taskName);
        
//        // 计划开始时间：今天的指定时间
//        LocalDateTime scheduledStartTime = LocalDateTime.of(LocalDate.now(),
//            schedule.getScheduledStartTime());
//        task.setScheduledStartTime(DateUtils.toDate(scheduledStartTime));

        // 时长
        task.setDuration(schedule.getDuration());
        
        // 创建信息
        task.setCreateBy("system");
        task.setCreateTime(DateUtils.getNowDate());
        task.setDelFlag("0");
        
        return task;
    }
    
    /**
     * 测试方法 - 手动触发任务生成
     */
    public void testGenerateTasks() {
        log.info("手动触发巡检任务生成");
        generateDailyTasks();
    }
}
